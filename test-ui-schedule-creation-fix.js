require('dotenv').config({ path: '.env.local' });

async function testUIScheduleCreationFix() {
  try {
    console.log('🧪 Testing UI schedule creation fix...');
    
    // Import the API method that the UI uses
    const { schedulesApi } = await import('./src/lib/api.js');
    
    // Test data similar to what the UI would send
    const scheduleData = {
      project_id: 'test-project-id',
      scheduled_date: '2025-01-25T10:00:00Z',
      scheduled_end_date: '2025-01-25T16:00:00Z',
      pilot_id: 'test-pilot-id',
      amount: 5000,
      location: 'Test Location for UI Fix',
      notes: 'Test schedule creation via UI flow (fixed)',
      is_recurring: false,
      is_outsourced: false
    };
    
    const vendors = []; // Empty vendors array for non-outsourced schedule
    
    console.log('📝 Creating schedule via schedulesApi.createWithVendors (UI flow)...');
    
    try {
      const result = await schedulesApi.createWithVendors(scheduleData, vendors);
      console.log('✅ Schedule created successfully:', {
        id: result.id,
        custom_id: result.custom_id,
        sharepoint_folder_id: result.sharepoint_folder_id,
        sharepoint_folder_url: result.sharepoint_folder_url,
        sharepoint_share_link: result.sharepoint_share_link
      });
      
      // Wait a bit for background job to process
      console.log('⏳ Waiting for background job to process...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check if SharePoint folder was created by re-fetching the schedule
      const updatedSchedule = await schedulesApi.getById(result.id);
      console.log('📁 SharePoint folder status:', {
        folder_id: updatedSchedule.sharepoint_folder_id,
        folder_url: updatedSchedule.sharepoint_folder_url,
        share_link: updatedSchedule.sharepoint_share_link
      });
      
      if (updatedSchedule.sharepoint_folder_id) {
        console.log('🎉 SUCCESS: SharePoint folder was created!');
      } else {
        console.log('❌ ISSUE: SharePoint folder was not created yet');
      }
      
    } catch (error) {
      console.error('❌ Error creating schedule:', error.message);
      
      // If it's a function not found error, that confirms our fix is needed
      if (error.message.includes('create_schedule_with_vendors_v3')) {
        console.log('✅ CONFIRMED: The v3 function doesn\'t exist - our fix should resolve this');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testUIScheduleCreationFix();