#!/usr/bin/env node

// Check for mismatches between CLIENT_TYPES and DEFAULT_TASK_TEMPLATES

const CLIENT_TYPES = [
  'Wedding',
  'Corporate',
  'Movie',
  'Govt',
  'NGO',
  'Survey',
  'Surveillance',
  'Real estate',
  'News',
  'Event',
  'Collaboration',
];

const DEFAULT_TASK_TEMPLATES_KEYS = [
  'Wedding',
  'Movie',
  'Surveillance',
  'Event',
  'News',
  'Collaboration',
  'Corporate',
  'Real estate',
  'Govt',
  'NGO',
  'Survey',
];

console.log('🔍 Checking client type mismatches...\n');

console.log('CLIENT_TYPES from constants.ts:');
CLIENT_TYPES.forEach(type => console.log(`  - "${type}"`));

console.log('\nDEFAULT_TASK_TEMPLATES keys from default-tasks.ts:');
DEFAULT_TASK_TEMPLATES_KEYS.forEach(type => console.log(`  - "${type}"`));

console.log('\n📋 Analysis:');

// Check for types in CLIENT_TYPES but not in DEFAULT_TASK_TEMPLATES
const missingInTemplates = CLIENT_TYPES.filter(type => !DEFAULT_TASK_TEMPLATES_KEYS.includes(type));
if (missingInTemplates.length > 0) {
  console.log('❌ Client types missing in DEFAULT_TASK_TEMPLATES:');
  missingInTemplates.forEach(type => console.log(`  - "${type}"`));
} else {
  console.log('✅ All CLIENT_TYPES have corresponding DEFAULT_TASK_TEMPLATES');
}

// Check for types in DEFAULT_TASK_TEMPLATES but not in CLIENT_TYPES
const missingInConstants = DEFAULT_TASK_TEMPLATES_KEYS.filter(type => !CLIENT_TYPES.includes(type));
if (missingInConstants.length > 0) {
  console.log('❌ Template keys missing in CLIENT_TYPES:');
  missingInConstants.forEach(type => console.log(`  - "${type}"`));
} else {
  console.log('✅ All DEFAULT_TASK_TEMPLATES keys exist in CLIENT_TYPES');
}

console.log('\n🔧 Recommendation:');
if (missingInTemplates.length > 0 || missingInConstants.length > 0) {
  console.log('There are mismatches that could cause default tasks not to be created!');
  console.log('When a schedule is created with a client type that has no matching template,');
  console.log('the getDefaultTasksForClientType() function returns an empty array.');
} else {
  console.log('No mismatches found. The issue might be elsewhere.');
}