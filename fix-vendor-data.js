const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU'

console.log('🔗 Connecting to Supabase...')
const supabase = createClient(supabaseUrl, supabaseKey)

async function fixVendorData() {
  console.log('🔧 Fixing vendor data inconsistencies...')
  
  // 1. Check all vendors and their linkage status
  const { data: vendors, error: vendorsError } = await supabase
    .from('outsourcing_vendors')
    .select(`
      id,
      name,
      custom_id,
      created_at
    `)
    .order('created_at', { ascending: false })
    
  if (vendorsError) {
    console.error('❌ Error fetching vendors:', vendorsError)
    return
  }
  
  console.log(`📋 Found ${vendors.length} vendors:`)
  
  for (const vendor of vendors) {
    console.log(`\n🏢 Vendor: ${vendor.name} (${vendor.custom_id})`)
    
    // Check schedule linkages
    const { data: scheduleLinks, error: linksError } = await supabase
      .from('schedule_vendors')
      .select('schedule_id, cost')
      .eq('vendor_id', vendor.id)
      
    if (linksError) {
      console.error(`  ❌ Error checking links: ${linksError.message}`)
      continue
    }
    
    console.log(`  📅 Linked schedules: ${scheduleLinks.length}`)
    
    // Check expenses linked to this vendor
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .select('id, amount, description, category, project_id')
      .eq('vendor_id', vendor.id)
      
    if (expensesError) {
      console.error(`  ❌ Error checking expenses: ${expensesError.message}`)
      continue
    }
    
    console.log(`  💰 Linked expenses: ${expenses.length}`)
    if (expenses.length > 0) {
      expenses.forEach(expense => {
        console.log(`    - ₹${expense.amount}: ${expense.description} (${expense.category})`)
      })
    }
    
    // Check if vendor has any projects that should be linked
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, vendor_payment_status, vendor_payment_amount')
      .not('vendor_payment_amount', 'is', null)
      .gt('vendor_payment_amount', 0)
      
    if (!projectsError && projects.length > 0) {
      console.log(`  🏗️ Projects with vendor payments: ${projects.length}`)
      projects.forEach(project => {
        console.log(`    - ${project.name}: ₹${project.vendor_payment_amount} (${project.vendor_payment_status})`)
      })
    }
  }
  
  // 2. Check for orphaned schedules that should be outsourced
  const { data: allSchedules, error: schedulesError } = await supabase
    .from('schedules')
    .select(`
      id,
      project_id,
      scheduled_date,
      is_outsourced,
      project:projects(name)
    `)
    .order('scheduled_date', { ascending: false })
    .limit(20)
    
  if (schedulesError) {
    console.error('❌ Error fetching schedules:', schedulesError)
    return
  }
  
  console.log(`\n📅 Recent schedules (last 20):`)
  allSchedules.forEach(schedule => {
    console.log(`  - ${schedule.project?.name} (${schedule.scheduled_date}) - Outsourced: ${schedule.is_outsourced}`)
  })
  
  // 3. Suggest fixes
  console.log(`\n💡 Suggested fixes:`)
  console.log(`1. If a vendor should have payments, create a schedule and link it via schedule_vendors table`)
  console.log(`2. If a payment was made, record it as an expense with vendor_id`)
  console.log(`3. If a schedule should be outsourced, set is_outsourced=true and link vendor`)
  console.log(`4. Run updateVendorPaymentStatus() for affected projects`)
}

fixVendorData().catch(console.error)