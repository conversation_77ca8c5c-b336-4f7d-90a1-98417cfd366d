const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU'

console.log('🔗 Connecting to Supabase...')
const supabase = createClient(supabaseUrl, supabaseKey)

async function checkTableStructures() {
  console.log('📋 Checking table structures...')
  
  // Check projects table
  console.log('\n🏗️ Projects table:')
  const { data: projects, error: projectsError } = await supabase
    .from('projects')
    .select('*')
    .limit(1)
    
  if (projectsError) {
    console.error('❌ Error fetching projects:', projectsError)
  } else {
    console.log('✅ Projects table accessible')
    if (projects && projects.length > 0) {
      console.log('Sample project structure:', Object.keys(projects[0]))
    } else {
      console.log('No projects found')
    }
  }
  
  // Check clients table
  console.log('\n👥 Clients table:')
  const { data: clients, error: clientsError } = await supabase
    .from('clients')
    .select('*')
    .limit(1)
    
  if (clientsError) {
    console.error('❌ Error fetching clients:', clientsError)
  } else {
    console.log('✅ Clients table accessible')
    if (clients && clients.length > 0) {
      console.log('Sample client structure:', Object.keys(clients[0]))
    } else {
      console.log('No clients found')
    }
  }
  
  // Check expenses table
  console.log('\n💰 Expenses table:')
  const { data: expenses, error: expensesError } = await supabase
    .from('expenses')
    .select('*')
    .limit(1)
    
  if (expensesError) {
    console.error('❌ Error fetching expenses:', expensesError)
  } else {
    console.log('✅ Expenses table accessible')
    if (expenses && expenses.length > 0) {
      console.log('Sample expense structure:', Object.keys(expenses[0]))
    } else {
      console.log('No expenses found')
    }
  }
  
  // Check schedules table
  console.log('\n📅 Schedules table:')
  const { data: schedules, error: schedulesError } = await supabase
    .from('schedules')
    .select('*')
    .limit(1)
    
  if (schedulesError) {
    console.error('❌ Error fetching schedules:', schedulesError)
  } else {
    console.log('✅ Schedules table accessible')
    if (schedules && schedules.length > 0) {
      console.log('Sample schedule structure:', Object.keys(schedules[0]))
    } else {
      console.log('No schedules found')
    }
  }
}

checkTableStructures().catch(console.error)