require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkFunctions() {
  console.log('🔍 Checking PostgreSQL functions...');
  
  try {
    // Query the pg_proc table to find functions
    const { data, error } = await supabase
      .from('pg_proc')
      .select('proname, prosrc')
      .ilike('proname', '%schedule%vendor%');
    
    if (error) {
      console.log('❌ Error querying pg_proc:', error.message);
      
      // Try alternative approach - query information_schema
      const { data: schemaData, error: schemaError } = await supabase
        .rpc('sql', { 
          query: `
            SELECT routine_name, routine_definition 
            FROM information_schema.routines 
            WHERE routine_name LIKE '%schedule%vendor%'
            AND routine_schema = 'public'
          `
        });
      
      if (schemaError) {
        console.log('❌ Error querying information_schema:', schemaError.message);
      } else {
        console.log('📋 Functions from information_schema:', schemaData);
      }
    } else {
      console.log('📋 Functions from pg_proc:', data);
    }
    
    // Test specific functions
    console.log('\n🧪 Testing specific functions...');
    
    // Test create_schedule_with_vendors_v3
    try {
      const { data: v3Data, error: v3Error } = await supabase.rpc('create_schedule_with_vendors_v3', {
        p_title: 'Test Schedule',
        p_scheduled_date: '2025-01-15',
        p_location: 'Test Location',
        p_project_id: '00000000-0000-0000-0000-000000000000',
        p_vendor_ids: []
      });
      
      if (v3Error) {
        console.log('❌ create_schedule_with_vendors_v3 error:', v3Error.message);
      } else {
        console.log('✅ create_schedule_with_vendors_v3 exists and works');
      }
    } catch (err) {
      console.log('❌ create_schedule_with_vendors_v3 exception:', err.message);
    }
    
    // Test create_schedule_with_vendors
    try {
      const { data: regularData, error: regularError } = await supabase.rpc('create_schedule_with_vendors', {
        p_title: 'Test Schedule',
        p_scheduled_date: '2025-01-15',
        p_location: 'Test Location',
        p_project_id: '00000000-0000-0000-0000-000000000000',
        p_vendor_ids: []
      });
      
      if (regularError) {
        console.log('❌ create_schedule_with_vendors error:', regularError.message);
      } else {
        console.log('✅ create_schedule_with_vendors exists and works');
      }
    } catch (err) {
      console.log('❌ create_schedule_with_vendors exception:', err.message);
    }
    
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
}

checkFunctions();