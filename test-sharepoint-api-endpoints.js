#!/usr/bin/env node

/**
 * Test Enhanced SharePoint Folder Creation System via API Endpoints
 * This script tests the enhanced SharePoint folder creation system by calling API endpoints
 */

const dotenv = require('dotenv')
const { createClient } = require('@supabase/supabase-js')

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

const API_BASE_URL = 'http://localhost:3002'

async function testEnhancedSharePointSystem() {
  console.log('🧪 Testing Enhanced SharePoint Folder Creation System via API')
  console.log('============================================================\n')

  try {
    // 1. Get test data
    console.log('1️⃣ Getting test data...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select('id, custom_id')
      .limit(1)
      .single()

    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
      .single()

    if (!projects || !users) {
      console.log('❌ No test data available')
      return
    }

    console.log(`   ✅ Using project: ${projects.custom_id}`)
    console.log(`   ✅ Using user: ${users.name}`)

    // 2. Test schedule creation with enhanced SharePoint system
    console.log('\n2️⃣ Creating test schedule with enhanced SharePoint system...')
    console.log('   🔄 Testing enhanced createWithVendors API...')

    const createResponse = await fetch(`${API_BASE_URL}/api/test-createwithvendors`, {
      method: 'GET'
    })

    if (!createResponse.ok) {
      const errorText = await createResponse.text()
      console.log(`   ❌ Schedule creation failed: ${createResponse.status} ${createResponse.statusText}`)
      console.log(`   Error: ${errorText}`)
      return
    }

    const createResult = await createResponse.json()
    console.log('   📋 API Response:', JSON.stringify(createResult, null, 2))
    
    const scheduleId = createResult.schedule?.id
    const customId = createResult.schedule?.custom_id
    
    console.log(`   ✅ Schedule created: ${customId} (${scheduleId})`)

    // 3. Wait and check if SharePoint folder was created
    console.log('\n3️⃣ Checking SharePoint folder creation...')
    console.log('   ⏳ Waiting 5 seconds for background job...')
    await new Promise(resolve => setTimeout(resolve, 5000))

    const { data: scheduleCheck } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url')
      .eq('id', scheduleId)
      .single()

    if (scheduleCheck?.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created via background job')
      console.log(`   📁 Folder ID: ${scheduleCheck.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${scheduleCheck.sharepoint_folder_url}`)
    } else {
      console.log('   ⚠️  SharePoint folder not created via background job')
      
      // 4. Test synchronous SharePoint folder creation API
      console.log('\n4️⃣ Testing synchronous SharePoint folder creation...')
      
      const syncResponse = await fetch(`${API_BASE_URL}/api/ensure-sharepoint-folder-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ scheduleId })
      })

      if (syncResponse.ok) {
        const syncResult = await syncResponse.json()
        console.log('   ✅ Synchronous SharePoint folder creation successful')
        console.log(`   📁 Result: ${JSON.stringify(syncResult, null, 2)}`)
      } else {
        const errorText = await syncResponse.text()
        console.log(`   ❌ Synchronous SharePoint folder creation failed: ${syncResponse.status}`)
        console.log(`   Error: ${errorText}`)
      }
    }

    // 5. Check background job status
    console.log('\n5️⃣ Checking background job status...')
    
    const jobStatusResponse = await fetch(`${API_BASE_URL}/api/background-jobs`)
    
    if (jobStatusResponse.ok) {
        const jobStatus = await jobStatusResponse.json()
        console.log('   ✅ Background job status retrieved')
        console.log(`   📋 Background jobs: ${jobStatus.jobs?.length || 0} jobs in queue`)
        
        if (jobStatus.jobs?.length > 0) {
          console.log('   📋 Active jobs:')
          jobStatus.jobs.forEach((job, index) => {
            console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status} - ${job.entityType}:${job.entityId}`)
          })
        } else {
          console.log('   ℹ️ No background jobs in queue')
        }
      } else {
        console.log('   ❌ Failed to get background job status')
      }

    // 6. Cleanup
    console.log('\n6️⃣ Cleaning up test schedule...')
    
    const { error: deleteError } = await supabase
      .from('schedules')
      .delete()
      .eq('id', scheduleId)

    if (deleteError) {
      console.log(`   ⚠️  Failed to delete test schedule: ${deleteError.message}`)
    } else {
      console.log('   ✅ Test schedule cleaned up')
    }

    console.log('\n🎉 Test completed successfully!')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('   Full error:', error)
  }
}

// Run the test
testEnhancedSharePointSystem().catch(console.error)