const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU'
)

async function comprehensiveVendorCheck() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  console.log('🔍 COMPREHENSIVE VENDOR PAYMENT CHECK')
  console.log('=====================================')
  console.log('Vendor ID:', vendorId)
  
  try {
    // 1. Check vendor details
    console.log('\n1️⃣ VENDOR DETAILS:')
    const { data: vendor, error: vendorError } = await supabase
      .from('outsourcing_vendors')
      .select('*')
      .eq('id', vendorId)
      .single()
    
    if (vendorError) {
      console.error('❌ Vendor error:', vendorError)
      return
    }
    
    console.log('✅ Vendor found:', vendor.name)
    
    // 2. Check schedule_vendors links
    console.log('\n2️⃣ SCHEDULE_VENDORS LINKS:')
    const { data: scheduleVendors, error: svError } = await supabase
      .from('schedule_vendors')
      .select('*')
      .eq('vendor_id', vendorId)
    
    if (svError) {
      console.error('❌ Schedule vendors error:', svError)
      return
    }
    
    console.log(`✅ Found ${scheduleVendors.length} schedule-vendor links:`)
    scheduleVendors.forEach((sv, index) => {
      console.log(`   ${index + 1}. Schedule ID: ${sv.schedule_id}, Cost: ₹${sv.cost || 0}`)
    })
    
    // 3. Check schedules with project data
    console.log('\n3️⃣ SCHEDULES WITH PROJECT DATA:')
    const scheduleIds = scheduleVendors.map(sv => sv.schedule_id)
    
    if (scheduleIds.length === 0) {
      console.log('❌ No schedules found for this vendor')
      return
    }
    
    const { data: schedules, error: schedulesError } = await supabase
      .from('schedules')
      .select(`
        *,
        project:projects(
          *,
          client:clients(name)
        )
      `)
      .in('id', scheduleIds)
    
    if (schedulesError) {
      console.error('❌ Schedules error:', schedulesError)
      return
    }
    
    console.log(`✅ Found ${schedules.length} schedules:`)
    schedules.forEach((schedule, index) => {
      const project = schedule.project
      console.log(`   ${index + 1}. Schedule: ${schedule.id}`)
      console.log(`      Project: ${project?.name || 'Unknown'} (${project?.id})`)
      console.log(`      Client: ${project?.client?.name || 'Unknown'}`)
      console.log(`      Vendor Payment Status: ${project?.vendor_payment_status || 'Not set'}`)
      console.log(`      Vendor Payment Amount: ₹${project?.vendor_payment_amount || 0}`)
      console.log(`      Schedule Date: ${schedule.scheduled_date}`)
      console.log(`      Is Outsourced: ${schedule.is_outsourced}`)
    })
    
    // 4. Check expenses for these projects
    console.log('\n4️⃣ EXPENSES FOR THESE PROJECTS:')
    const projectIds = [...new Set(schedules.map(s => s.project_id))]
    
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .select('*')
      .in('project_id', projectIds)
      .eq('category', 'outsourcing')
    
    if (expensesError) {
      console.error('❌ Expenses error:', expensesError)
    } else {
      console.log(`✅ Found ${expenses.length} outsourcing expenses:`)
      expenses.forEach((expense, index) => {
        console.log(`   ${index + 1}. Project: ${expense.project_id}`)
        console.log(`      Amount: ₹${expense.amount}`)
        console.log(`      Description: ${expense.description}`)
        console.log(`      Date: ${expense.expense_date}`)
        console.log(`      Vendor ID: ${expense.vendor_id || 'Not set'}`)
      })
    }
    
    // 5. Test the actual API endpoint
    console.log('\n5️⃣ TESTING VENDOR-SHOOTS API:')
    const response = await fetch(`http://localhost:3002/api/vendor-shoots?vendorId=${vendorId}`)
    const apiData = await response.json()
    
    if (!response.ok) {
      console.error('❌ API error:', apiData)
      return
    }
    
    console.log(`✅ API returned ${apiData.shoots?.length || 0} shoots:`)
    apiData.shoots?.forEach((shoot, index) => {
      const vendorEntry = shoot.vendors?.find(sv => sv.vendor_id === vendorId)
      console.log(`   ${index + 1}. Shoot: ${shoot.id}`)
      console.log(`      Project: ${shoot.project?.name || 'Unknown'}`)
      console.log(`      Vendor Cost: ₹${vendorEntry?.cost || 0}`)
      console.log(`      Project Payment Status: ${shoot.project?.vendor_payment_status || 'Not set'}`)
      console.log(`      Project Payment Amount: ₹${shoot.project?.vendor_payment_amount || 0}`)
    })
    
    // 6. Calculate totals like frontend does
    console.log('\n6️⃣ FRONTEND CALCULATION SIMULATION:')
    const totalSpent = apiData.shoots?.reduce((sum, shoot) => {
      const vendorEntry = shoot.vendors?.find(sv => sv.vendor_id === vendorId)
      return sum + (vendorEntry?.cost || 0)
    }, 0) || 0
    
    let totalPaid = 0
    let totalPending = 0
    let paidProjects = 0
    let pendingProjects = 0
    
    apiData.shoots?.forEach(shoot => {
      const vendorEntry = shoot.vendors?.find(sv => sv.vendor_id === vendorId)
      const cost = vendorEntry?.cost || 0
      const paymentStatus = shoot.project?.vendor_payment_status || 'pending'
      const paymentAmount = shoot.project?.vendor_payment_amount || cost
      
      switch (paymentStatus) {
        case 'paid':
          totalPaid += paymentAmount
          paidProjects += 1
          break
        case 'pending':
        case 'overdue':
        default:
          totalPending += cost
          pendingProjects += 1
          break
      }
    })
    
    console.log('📊 CALCULATED TOTALS:')
    console.log(`   Total Spent: ₹${totalSpent}`)
    console.log(`   Total Paid: ₹${totalPaid}`)
    console.log(`   Total Pending: ₹${totalPending}`)
    console.log(`   Paid Projects: ${paidProjects}`)
    console.log(`   Pending Projects: ${pendingProjects}`)
    
    // 7. Check if expenses should be included
    console.log('\n7️⃣ EXPENSE INTEGRATION ANALYSIS:')
    console.log('Current system uses:')
    console.log('   - schedule_vendors.cost for vendor costs')
    console.log('   - projects.vendor_payment_status for payment status')
    console.log('   - projects.vendor_payment_amount for payment amounts')
    console.log('')
    console.log('Expenses table is NOT currently used for vendor payment calculations.')
    console.log('This might be the source of confusion.')
    
    if (expenses.length > 0) {
      const totalExpenseAmount = expenses.reduce((sum, exp) => sum + exp.amount, 0)
      console.log(`\nIf expenses were included, total would be: ₹${totalExpenseAmount}`)
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

comprehensiveVendorCheck()