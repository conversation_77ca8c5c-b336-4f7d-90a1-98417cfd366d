const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'
)

async function debugPaymentStatus() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  console.log('🔍 DEBUGGING PAYMENT STATUS CALCULATION')
  console.log('======================================')
  
  // 1. Get all projects that have schedules linked to this vendor
  console.log('\n1️⃣ PROJECTS WITH VENDOR SCHEDULES:')
  const { data: scheduleVendors, error: svError } = await supabase
    .from('schedule_vendors')
    .select(`
      *,
      schedules!inner (
        id,
        project_id,
        scheduled_date,
        notes
      )
    `)
    .eq('vendor_id', vendorId)
    
  if (svError) {
    console.error('❌ Error:', svError)
    return
  }
  
  // Group by project
  const projectCosts = {}
  scheduleVendors.forEach(sv => {
    const projectId = sv.schedules.project_id
    if (!projectCosts[projectId]) {
      projectCosts[projectId] = {
        totalCost: 0,
        schedules: []
      }
    }
    projectCosts[projectId].totalCost += sv.cost || 0
    projectCosts[projectId].schedules.push({
      id: sv.schedules.id,
      cost: sv.cost,
      notes: sv.schedules.notes,
      date: sv.schedules.scheduled_date
    })
  })
  
  console.log(`✅ Found ${Object.keys(projectCosts).length} projects:`)
  
  // 2. Get project details and payment status
  console.log('\n2️⃣ PROJECT PAYMENT STATUS:')
  let totalPaid = 0
  let totalPending = 0
  let paidProjects = 0
  let pendingProjects = 0
  
  for (const [projectId, costData] of Object.entries(projectCosts)) {
    const { data: project, error: projError } = await supabase
      .from('projects')
      .select('id, name, vendor_payment_status, vendor_payment_amount')
      .eq('id', projectId)
      .single()
      
    if (projError) {
      console.error(`❌ Error fetching project ${projectId}:`, projError)
      continue
    }
    
    console.log(`\n📋 Project: ${project.name}`)
    console.log(`   ID: ${project.id}`)
    console.log(`   Total Cost: ₹${costData.totalCost}`)
    console.log(`   Payment Status: ${project.vendor_payment_status || 'null'}`)
    console.log(`   Payment Amount: ₹${project.vendor_payment_amount || 0}`)
    console.log(`   Schedules: ${costData.schedules.length}`)
    
    // Calculate based on payment status
    const cost = costData.totalCost
    const paymentAmount = project.vendor_payment_amount || cost
    
    switch (project.vendor_payment_status) {
      case 'paid':
        totalPaid += paymentAmount
        paidProjects += 1
        console.log(`   ✅ PAID: ₹${paymentAmount}`)
        break
      case 'pending':
      case 'overdue':
      default:
        totalPending += cost
        pendingProjects += 1
        console.log(`   ⏳ PENDING: ₹${cost}`)
        break
    }
  }
  
  console.log('\n3️⃣ FINAL CALCULATIONS:')
  console.log(`💰 Total Paid: ₹${totalPaid} (${paidProjects} projects)`)
  console.log(`⏳ Total Pending: ₹${totalPending} (${pendingProjects} projects)`)
  console.log(`📊 Grand Total: ₹${totalPaid + totalPending}`)
  
  // 4. Check what the API returns
  console.log('\n4️⃣ SIMULATING API RESPONSE:')
  try {
    const response = await fetch(`http://localhost:3002/api/vendor-shoots?vendorId=${vendorId}`)
    if (response.ok) {
      const apiData = await response.json()
      console.log(`✅ API returned ${apiData.shoots?.length || 0} shoots`)
      
      // Simulate frontend calculation
      const payments = apiData.shoots?.map(shoot => {
        const vendorEntry = shoot.vendors?.find(sv => sv.vendor_id === vendorId)
        return {
          outsourcing_cost: vendorEntry?.cost || 0,
          payment_status: shoot.project?.vendor_payment_status || 'pending',
          payment_amount: shoot.project?.vendor_payment_amount || (vendorEntry?.cost || 0)
        }
      }) || []
      
      let apiPaid = 0
      let apiPending = 0
      
      payments.forEach(payment => {
        const cost = payment.outsourcing_cost || 0
        const paymentAmount = payment.payment_amount || cost

        switch (payment.payment_status) {
          case 'paid':
            apiPaid += paymentAmount
            break
          case 'pending':
          case 'overdue':
          default:
            apiPending += cost
            break
        }
      })
      
      console.log(`🔄 API Simulation - Paid: ₹${apiPaid}, Pending: ₹${apiPending}`)
    } else {
      console.log('❌ API request failed')
    }
  } catch (error) {
    console.log('❌ API error:', error.message)
  }
}

debugPaymentStatus().catch(console.error)