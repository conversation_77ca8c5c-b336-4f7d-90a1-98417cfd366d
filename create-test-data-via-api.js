import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const API_BASE = 'http://localhost:3000/api'

async function createTestDataViaAPI() {
  console.log('🔧 Creating test data via API...\n')
  
  try {
    // 1. Create a test client
    console.log('👥 Step 1: Creating test client...')
    const clientData = {
      custom_id: 'CYM-001',
      name: 'Test Client',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Test Street, Test City',
      has_gst: false,
      client_type: 'corporate'
    }
    
    const clientResponse = await fetch(`${API_BASE}/clients`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(clientData)
    })
    
    if (!clientResponse.ok) {
      const errorText = await clientResponse.text()
      throw new Error(`Failed to create client: ${clientResponse.status} - ${errorText}`)
    }
    
    const client = await clientResponse.json()
    console.log(`   ✅ Client created: ${client.custom_id} - ${client.name}`)
    
    // 2. Create a test pilot (we'll need to create this directly since there's no users API)
    console.log('\n👨‍✈️ Step 2: Creating test pilot...')
    console.log('   ⚠️  Note: Creating pilot directly via database (no users API)')
    
    // We'll need to create the pilot using a direct database call with service role
    const { createClient } = await import('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    const { data: pilot, error: pilotError } = await supabase
      .from('users')
      .insert({
        email: '<EMAIL>',
        name: 'Test Pilot',
        role: 'pilot'
      })
      .select('id, name, role')
      .single()
    
    if (pilotError) {
      throw new Error(`Failed to create pilot: ${pilotError.message}`)
    }
    
    console.log(`   ✅ Pilot created: ${pilot.name} (${pilot.role})`)
    
    // 3. Create a test project
    console.log('\n📁 Step 3: Creating test project...')
    const projectData = {
      custom_id: 'PRJ-001',
      name: 'Test Project',
      description: 'A test project for folder creation testing',
      client_id: client.id,
      location: 'Test Location',
      status: 'active',
      total_amount: 10000,
      gst_inclusive: true,
      amount_received: 5000,
      amount_pending: 5000
    }
    
    const projectResponse = await fetch(`${API_BASE}/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    })
    
    if (!projectResponse.ok) {
      const errorText = await projectResponse.text()
      throw new Error(`Failed to create project: ${projectResponse.status} - ${errorText}`)
    }
    
    const project = await projectResponse.json()
    console.log(`   ✅ Project created: ${project.custom_id} - ${project.name}`)
    
    // 4. Create a test schedule
    console.log('\n📅 Step 4: Creating test schedule...')
    const scheduleData = {
      project_id: project.id,
      scheduled_date: '2025-01-15',
      scheduled_end_date: '2025-01-15',
      status: 'scheduled',
      pilot_id: pilot.id,
      amount: 5000,
      location: 'Test Location',
      notes: 'Test schedule for folder creation verification',
      is_recurring: false,
      is_outsourced: false
    }
    
    const scheduleResponse = await fetch(`${API_BASE}/schedules`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(scheduleData)
    })
    
    if (!scheduleResponse.ok) {
      const errorText = await scheduleResponse.text()
      throw new Error(`Failed to create schedule: ${scheduleResponse.status} - ${errorText}`)
    }
    
    const schedule = await scheduleResponse.json()
    console.log(`   ✅ Schedule created: ${schedule.custom_id}`)
    console.log(`   📅 Date: ${schedule.scheduled_date}`)
    console.log(`   📍 Location: ${schedule.location}`)
    console.log(`   💰 Amount: ${schedule.amount}`)
    
    console.log('\n🎉 Test data created successfully!')
    console.log('\n📊 Summary:')
    console.log(`   - Client: ${client.custom_id} - ${client.name}`)
    console.log(`   - Pilot: ${pilot.name} (${pilot.role})`)
    console.log(`   - Project: ${project.custom_id} - ${project.name}`)
    console.log(`   - Schedule: ${schedule.custom_id} - ${schedule.scheduled_date}`)
    
    console.log('\n🚀 Now you can test the SharePoint folder creation!')
    console.log(`   Schedule ID: ${schedule.id}`)
    console.log(`   Schedule Custom ID: ${schedule.custom_id}`)
    
    // 5. Check if SharePoint folder creation was triggered
    console.log('\n🔍 Step 5: Checking SharePoint folder creation...')
    
    // Wait a moment for background job to be queued
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Check background jobs
    const jobsResponse = await fetch(`${API_BASE}/background-jobs`)
    if (jobsResponse.ok) {
      const jobsData = await jobsResponse.json()
      console.log(`   📋 Background jobs: ${jobsData.jobs?.length || 0} jobs in queue`)
      
      if (jobsData.jobs && jobsData.jobs.length > 0) {
        jobsData.jobs.forEach((job, index) => {
          console.log(`      ${index + 1}. ${job.type} - ${job.entityType} - ${job.status}`)
        })
      }
    }
    
    return {
      client,
      pilot,
      project,
      schedule
    }
    
  } catch (error) {
    console.error('❌ Failed to create test data:', error.message)
    throw error
  }
}

createTestDataViaAPI()
  .then((data) => {
    console.log('\n🏁 Test data creation completed')
    console.log('\n💡 Next steps:')
    console.log('   1. The schedule should automatically trigger SharePoint folder creation')
    console.log('   2. Wait a few seconds for background job to process')
    console.log('   3. Check the schedule again: node check-schedules.js')
    console.log('   4. Or manually trigger folder creation:')
    console.log(`      curl -X POST http://localhost:3000/api/ensure-sharepoint-folder -H "Content-Type: application/json" -d '{"scheduleId":"${data.schedule.id}"}'`)
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Test data creation failed:', error)
    process.exit(1)
  })
