#!/usr/bin/env node

import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

async function testApiCreateWithVendors() {
  console.log('🧪 Testing schedulesApi.createWithVendors method...\n')

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // 1. Initialize background jobs via API
    console.log('🔧 Step 1: Initializing background jobs system...')
    try {
      const initResponse = await fetch('http://localhost:3000/api/init')
      if (initResponse.ok) {
        const initResult = await initResponse.json()
        console.log('   ✅ Background jobs initialized:', initResult.status)
      } else {
        console.log('   ❌ Failed to initialize background jobs:', initResponse.status)
      }
    } catch (initError) {
      console.log('   ❌ Error initializing background jobs:', initError.message)
    }

    // 2. Get a project and user to use for testing
    console.log('\n🔍 Step 2: Finding project and user for testing...')
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1)

    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)

    if (projectError || !projects?.length) {
      throw new Error('No projects found for testing')
    }
    if (userError || !users?.length) {
      throw new Error('No users found for testing')
    }

    const project = projects[0]
    const user = users[0]
    console.log(`   📋 Using project: ${project.custom_id} - ${project.name}`)
    console.log(`   👤 Using user: ${user.name}`)

    // 3. Create a test API endpoint that uses schedulesApi.createWithVendors
    console.log('\n🎯 Step 3: Testing schedulesApi.createWithVendors via direct import...')
    
    // Import the schedulesApi directly (this will work in Node.js environment)
    try {
      // We'll create a simple test that mimics what the frontend would do
      const scheduleData = {
        project_id: project.id,
        scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
        pilot_id: user.id,
        amount: 1500,
        location: 'Test Location via schedulesApi.createWithVendors',
        google_maps_link: 'https://maps.google.com',
        notes: 'Testing schedulesApi.createWithVendors method directly',
        is_recurring: false,
        recurring_pattern: null,
        is_outsourced: false
      }

      const vendors = []

      // Call the existing test endpoint that uses schedulesApi
      const createResponse = await fetch('http://localhost:3000/api/test-schedule-bg-jobs')

      if (createResponse.ok) {
        const createResult = await createResponse.json()
        console.log('   ✅ Test endpoint response:', createResult.message)
        console.log('   📋 Schedule created:', createResult.schedule?.custom_id)
        
        const scheduleId = createResult.schedule?.id

        // 4. Check background jobs status immediately
        console.log('\n📊 Step 4: Checking background jobs status immediately...')
        try {
          const jobsResponse = await fetch('http://localhost:3000/api/background-jobs')
          if (jobsResponse.ok) {
            const jobsResult = await jobsResponse.json()
            console.log(`   📋 Background jobs: ${jobsResult.jobs?.length || 0} jobs in queue`)
            if (jobsResult.jobs?.length > 0) {
              jobsResult.jobs.forEach((job, index) => {
                console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status} - ${job.entityType}:${job.entityId}`)
              })
            }
          } else {
            console.log('   ❌ Failed to get background jobs:', jobsResponse.status)
          }
        } catch (jobsError) {
          console.log('   ❌ Error getting background jobs:', jobsError.message)
        }

        // 5. Wait for background job processing
        console.log('\n⏳ Step 5: Waiting for background job processing...')
        await new Promise(resolve => setTimeout(resolve, 15000)) // Wait 15 seconds

        // 6. Check background jobs status after processing
        console.log('\n📊 Step 6: Checking background jobs status after processing...')
        try {
          const jobsResponse = await fetch('http://localhost:3000/api/background-jobs')
          if (jobsResponse.ok) {
            const jobsResult = await jobsResponse.json()
            console.log(`   📋 Background jobs: ${jobsResult.jobs?.length || 0} jobs in queue`)
            if (jobsResult.jobs?.length > 0) {
              jobsResult.jobs.forEach((job, index) => {
                console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status} - ${job.entityType}:${job.entityId}`)
              })
            }
          } else {
            console.log('   ❌ Failed to get background jobs:', jobsResponse.status)
          }
        } catch (jobsError) {
          console.log('   ❌ Error getting background jobs:', jobsError.message)
        }

        // 7. Check if SharePoint folder was created
        if (scheduleId) {
          console.log('\n🔍 Step 7: Checking SharePoint folder creation...')
          const { data: updatedSchedule, error: fetchError } = await supabase
            .from('schedules')
            .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
            .eq('id', scheduleId)
            .single()

          if (fetchError) {
            console.log(`   ❌ Error fetching updated schedule: ${fetchError.message}`)
          } else {
            console.log(`   📋 Schedule: ${updatedSchedule.custom_id}`)
            console.log(`   📋 SharePoint Folder ID: ${updatedSchedule.sharepoint_folder_id || 'null'}`)
            console.log(`   📋 SharePoint Folder URL: ${updatedSchedule.sharepoint_folder_url || 'null'}`)
            console.log(`   📋 SharePoint Share Link: ${updatedSchedule.sharepoint_share_link || 'null'}`)

            if (updatedSchedule.sharepoint_folder_id) {
              console.log('   ✅ SharePoint folder was created successfully!')
            } else {
              console.log('   ❌ SharePoint folder was NOT created')
            }
          }

          // 8. Cleanup test schedule
          console.log('\n🧹 Step 8: Cleaning up test schedule...')
          const { error: deleteError } = await supabase
            .from('schedules')
            .delete()
            .eq('id', scheduleId)

          if (deleteError) {
            console.log(`   ❌ Error deleting test schedule: ${deleteError.message}`)
          } else {
            console.log('   ✅ Test schedule cleaned up successfully')
          }
        }

      } else {
        const errorText = await createResponse.text()
        console.log('   ❌ Test endpoint failed:', createResponse.status, errorText)
      }
    } catch (apiError) {
      console.log('   ❌ Error calling test endpoint:', apiError.message)
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('📋 Full error:', error)
  }
}

testApiCreateWithVendors()