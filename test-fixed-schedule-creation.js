import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testFixedScheduleCreation() {
  console.log('🧪 Testing fixed schedule creation with SharePoint folder...\n')

  try {
    // 1. Get a valid project, client, and pilot
    console.log('📋 Finding valid project, client, and pilot...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select('id, name, client_id')
      .limit(1)
    
    if (!projects || projects.length === 0) {
      throw new Error('No projects found')
    }

    const { data: pilots } = await supabase
      .from('users')
      .select('id, name')
      .eq('role', 'pilot')
      .limit(1)
    
    if (!pilots || pilots.length === 0) {
      throw new Error('No pilots found')
    }

    const project = projects[0]
    const pilot = pilots[0]
    
    console.log(`   ✅ Project: ${project.name} (${project.id})`)
    console.log(`   ✅ Pilot: ${pilot.name} (${pilot.id})`)

    // 2. Create a new schedule using the RPC function
    console.log('\n🔄 Creating new schedule...')
    
    const scheduleData = {
      project_id: project.id,
      pilot_id: pilot.id,
      scheduled_date: new Date().toISOString().split('T')[0],
      amount: 1500,
      location: 'Test Location for Fixed Creation',
      notes: 'Test schedule created to verify SharePoint folder fix'
    }

    const { data: schedule, error: scheduleError } = await supabase.rpc('create_schedule_with_vendors', {
      p_schedule_data: scheduleData,
      p_vendors: []
    })

    if (scheduleError) {
      throw new Error(`Schedule creation failed: ${scheduleError.message}`)
    }

    console.log(`   ✅ Schedule created: ${schedule.custom_id} (${schedule.id})`)

    // 3. Wait a moment for any async operations
    console.log('\n⏳ Waiting 3 seconds for SharePoint folder creation...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // 4. Check if SharePoint folder was created
    console.log('\n📁 Checking SharePoint folder creation...')
    
    const { data: updatedSchedule } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', schedule.id)
      .single()

    if (updatedSchedule?.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created successfully!')
      console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
      if (updatedSchedule.sharepoint_share_link) {
        console.log(`   🔗 Share Link: ${updatedSchedule.sharepoint_share_link}`)
      }
    } else {
      console.log('   ❌ SharePoint folder not created automatically')
      
      // Try manual trigger
      console.log('\n🔄 Trying manual SharePoint folder creation...')
      
      const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        console.log('   ✅ Manual trigger successful')
        
        // Check again
        const { data: finalSchedule } = await supabase
          .from('schedules')
          .select('sharepoint_folder_id, sharepoint_folder_url')
          .eq('id', schedule.id)
          .single()
        
        if (finalSchedule?.sharepoint_folder_id) {
          console.log('   ✅ SharePoint folder created after manual trigger!')
        }
      } else {
        console.log('   ❌ Manual trigger failed')
      }
    }

    console.log(`\n🎯 Test Complete! Schedule: ${schedule.custom_id}`)
    console.log('💡 You can clean up by deleting this schedule if needed.')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

testFixedScheduleCreation()