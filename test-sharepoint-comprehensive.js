#!/usr/bin/env node

/**
 * Comprehensive SharePoint functionality test
 * Tests both background jobs and direct API calls
 */

require('dotenv').config({ path: '.env.local' })

async function runComprehensiveTest() {
  console.log('🧪 Running comprehensive SharePoint functionality test...\n')

  try {
    // 1. Test environment setup
    console.log('1️⃣ Checking environment setup...')
    const requiredEnvs = ['MICROSOFT_CLIENT_ID', 'MICROSOFT_CLIENT_SECRET', 'MICROSOFT_TENANT_ID']
    const missingEnvs = requiredEnvs.filter(env => !process.env[env])
    
    if (missingEnvs.length > 0) {
      console.log('❌ Missing environment variables:', missingEnvs.join(', '))
      return
    }
    console.log('✅ All required environment variables are set')

    // 2. Test background job system
    console.log('\n2️⃣ Testing background job system...')
    
    // Check if background jobs are initialized
    const initResponse = await fetch('http://localhost:3000/api/init')
    if (initResponse.ok) {
      console.log('✅ Background job system initialized')
    } else {
      console.log('⚠️  Background job initialization response:', initResponse.status)
    }

    // Check current background jobs
    const jobsResponse = await fetch('http://localhost:3000/api/background-jobs')
    const jobsData = await jobsResponse.json()
    console.log(`✅ Background jobs API accessible (${jobsData.jobs?.length || 0} jobs in queue)`)

    // 3. Test schedule creation with background jobs
    console.log('\n3️⃣ Testing schedule creation with background jobs...')
    
    const scheduleResponse = await fetch('http://localhost:3000/api/test-schedule-bg-jobs', {
      method: 'GET'
    })

    if (!scheduleResponse.ok) {
      console.log('❌ Schedule creation failed:', scheduleResponse.status)
      return
    }

    const scheduleData = await scheduleResponse.json()
    console.log(`✅ Schedule created: ${scheduleData.schedule.custom_id}`)
    console.log(`   Schedule ID: ${scheduleData.schedule.id}`)

    // 4. Monitor background job processing
    console.log('\n4️⃣ Monitoring background job processing...')
    
    let attempts = 0
    const maxAttempts = 10
    let jobCompleted = false

    while (attempts < maxAttempts && !jobCompleted) {
      await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds
      attempts++

      const currentJobsResponse = await fetch('http://localhost:3000/api/background-jobs')
      const currentJobsData = await currentJobsResponse.json()
      
      const scheduleJob = currentJobsData.jobs?.find(job => 
        job.entityId === scheduleData.schedule.id && 
        job.type === 'sharepoint_folder_creation'
      )

      if (scheduleJob) {
        console.log(`   Attempt ${attempts}: Job status = ${scheduleJob.status} (${scheduleJob.attempts}/${scheduleJob.maxAttempts})`)
        
        if (scheduleJob.status === 'completed') {
          jobCompleted = true
          console.log('✅ Background job completed successfully')
        } else if (scheduleJob.status === 'failed') {
          console.log('❌ Background job failed:', scheduleJob.error)
          break
        }
      } else {
        console.log(`   Attempt ${attempts}: Job not found (may have been cleaned up)`)
        // If job is not found, check if folder was created
        break
      }
    }

    // 5. Verify SharePoint folder creation
    console.log('\n5️⃣ Verifying SharePoint folder creation...')
    
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    const { data: finalSchedule } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', scheduleData.schedule.id)
      .single()

    if (finalSchedule?.sharepoint_folder_id) {
      console.log('✅ SharePoint folder successfully created')
      console.log(`   📁 Folder ID: ${finalSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${finalSchedule.sharepoint_folder_url}`)
      console.log(`   📤 Share Link: ${finalSchedule.sharepoint_share_link}`)
    } else {
      console.log('❌ SharePoint folder was not created')
    }

    // 6. Test direct API call
    console.log('\n6️⃣ Testing direct SharePoint API call...')
    
    const directResponse = await fetch('http://localhost:3000/api/ensure-sharepoint-folder', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ scheduleId: scheduleData.schedule.id })
    })

    if (directResponse.ok) {
      const directResult = await directResponse.json()
      console.log('✅ Direct API call successful')
      console.log(`   Result: ${directResult.message}`)
    } else {
      console.log('❌ Direct API call failed:', directResponse.status)
    }

    // 7. Summary
    console.log('\n🎯 Test Summary:')
    console.log('✅ Environment variables configured')
    console.log('✅ Background job system working')
    console.log('✅ Schedule creation working')
    console.log(`${jobCompleted ? '✅' : '⚠️'} Background job processing ${jobCompleted ? 'working' : 'needs monitoring'}`)
    console.log(`${finalSchedule?.sharepoint_folder_id ? '✅' : '❌'} SharePoint folder creation ${finalSchedule?.sharepoint_folder_id ? 'working' : 'failed'}`)
    console.log('✅ Direct API calls working')

    console.log(`\n💡 Created schedule: ${scheduleData.schedule.custom_id} (${scheduleData.schedule.id})`)

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('   Full error:', error)
  }
}

// Run the comprehensive test
runComprehensiveTest().catch(console.error)