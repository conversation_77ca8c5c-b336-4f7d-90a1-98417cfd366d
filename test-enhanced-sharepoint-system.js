#!/usr/bin/env node

import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testEnhancedSharePointSystem() {
  console.log('🧪 Testing Enhanced SharePoint Folder Creation System')
  console.log('=' .repeat(60))

  try {
    // Step 1: Get a test project and user
    console.log('\n1️⃣ Getting test data...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select('*')
      .limit(1)
    
    const { data: users } = await supabase
      .from('users')
      .select('*')
      .limit(1)

    if (!projects?.length || !users?.length) {
      throw new Error('No test data available')
    }

    const project = projects[0]
    const user = users[0]
    
    console.log(`   ✅ Using project: ${project.custom_id}`)
    console.log(`   ✅ Using user: ${user.name}`)

    // Step 2: Create a test schedule using the enhanced API
    console.log('\n2️⃣ Creating test schedule with enhanced SharePoint system...')
    
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      pilot_id: user.id,
      amount: 1500,
      location: 'Enhanced Test Location',
      google_maps_link: 'https://maps.google.com/enhanced-test',
      notes: 'Testing enhanced SharePoint folder creation system',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }

    // Test the enhanced createWithVendors method
    console.log('   🔄 Testing enhanced createWithVendors...')
    
    // Import the API directly
    const { schedulesApi } = await import('./src/lib/api.ts')
    
    const schedule = await schedulesApi.createWithVendors(scheduleData, [])
    
    console.log(`   ✅ Schedule created: ${schedule.custom_id} (ID: ${schedule.id})`)

    // Step 3: Wait a moment and check if SharePoint folder was created
    console.log('\n3️⃣ Checking SharePoint folder creation...')
    
    // Wait 5 seconds for background job or synchronous execution
    console.log('   ⏳ Waiting 5 seconds for SharePoint folder creation...')
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    // Check if SharePoint folder was created
    const { data: updatedSchedule } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id')
      .eq('id', schedule.id)
      .single()
    
    if (updatedSchedule?.sharepoint_folder_id) {
      console.log(`   ✅ SharePoint folder created successfully: ${updatedSchedule.sharepoint_folder_id}`)
    } else {
      console.log('   ⚠️ SharePoint folder not yet created, testing synchronous API...')
      
      // Step 4: Test the synchronous API endpoint
      console.log('\n4️⃣ Testing synchronous SharePoint folder creation API...')
      
      const response = await fetch('http://localhost:3001/api/ensure-sharepoint-folder-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scheduleId: schedule.id
        })
      })
      
      if (response.ok) {
        const result = await response.json()
        console.log('   ✅ Synchronous API response:', result)
        
        // Check again if SharePoint folder was created
        const { data: finalSchedule } = await supabase
          .from('schedules')
          .select('sharepoint_folder_id')
          .eq('id', schedule.id)
          .single()
        
        if (finalSchedule?.sharepoint_folder_id) {
          console.log(`   ✅ SharePoint folder created via sync API: ${finalSchedule.sharepoint_folder_id}`)
        } else {
          console.log('   ❌ SharePoint folder still not created after sync API')
        }
      } else {
        console.log(`   ❌ Synchronous API failed: ${response.status}`)
        const errorText = await response.text()
        console.log(`   Error: ${errorText}`)
      }
    }

    // Step 5: Test background job status API
    console.log('\n5️⃣ Checking background job status...')
    
    try {
      const jobsResponse = await fetch('http://localhost:3001/api/background-jobs')
      if (jobsResponse.ok) {
        const jobsResult = await jobsResponse.json()
        console.log(`   📋 Background jobs: ${jobsResult.jobs?.length || 0} jobs in queue`)
        
        if (jobsResult.jobs?.length > 0) {
          jobsResult.jobs.forEach((job, index) => {
            console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status} - ${job.entityType}:${job.entityId}`)
            if (job.error) {
              console.log(`      Error: ${job.error}`)
            }
          })
        }
      } else {
        console.log('   ❌ Failed to get background jobs status')
      }
    } catch (jobsError) {
      console.log('   ❌ Error getting background jobs:', jobsError.message)
    }

    // Step 6: Cleanup
    console.log('\n6️⃣ Cleaning up test schedule...')
    
    const { error: deleteError } = await supabase
      .from('schedules')
      .delete()
      .eq('id', schedule.id)
    
    if (deleteError) {
      console.log(`   ❌ Error deleting test schedule: ${deleteError.message}`)
    } else {
      console.log('   ✅ Test schedule cleaned up successfully')
    }

    console.log('\n🎉 Enhanced SharePoint system test completed!')
    
  } catch (error) {
    console.error('\n❌ Test failed:', error)
    process.exit(1)
  }
}

// Run the test
testEnhancedSharePointSystem()
  .then(() => {
    console.log('\n✅ All tests completed successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Test suite failed:', error)
    process.exit(1)
  })