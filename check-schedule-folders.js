import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

// Use service role key to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function checkScheduleFolders() {
  console.log('🔍 Checking SharePoint folder status for recent schedules...\n')
  
  try {
    // Get recent schedules with SharePoint folder info
    const { data: schedules, error } = await supabase
      .from('schedules')
      .select(`
        id, 
        custom_id, 
        scheduled_date, 
        created_at,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link,
        project:projects(
          name,
          clients!inner(name, custom_id)
        )
      `)
      .order('created_at', { ascending: false })
      .limit(10)
    
    if (error) {
      throw new Error(`Failed to fetch schedules: ${error.message}`)
    }
    
    if (!schedules || schedules.length === 0) {
      console.log('❌ No schedules found')
      return
    }
    
    console.log(`📋 Found ${schedules.length} recent schedules:\n`)
    
    schedules.forEach((schedule, index) => {
      const hasFolder = !!schedule.sharepoint_folder_id
      const hasUrl = !!schedule.sharepoint_folder_url
      const hasShareLink = !!schedule.sharepoint_share_link
      
      const project = schedule.project
      const client = project?.clients
      
      console.log(`${index + 1}. ${schedule.custom_id}`)
      console.log(`   📅 Date: ${schedule.scheduled_date}`)
      console.log(`   📁 Project: ${project?.name || 'N/A'}`)
      console.log(`   👥 Client: ${client?.name || 'N/A'} (${client?.custom_id || 'N/A'})`)
      console.log(`   📁 Folder ID: ${hasFolder ? '✅ ' + schedule.sharepoint_folder_id : '❌ Missing'}`)
      console.log(`   🌐 Folder URL: ${hasUrl ? '✅ ' + schedule.sharepoint_folder_url : '❌ Missing'}`)
      console.log(`   🔗 Share Link: ${hasShareLink ? '✅ Present' : '❌ Missing'}`)
      console.log(`   📊 Status: ${hasFolder && hasUrl ? '✅ Complete' : '❌ Incomplete'}`)
      console.log('')
    })
    
    // Summary
    const completeSchedules = schedules.filter(s => s.sharepoint_folder_id && s.sharepoint_folder_url)
    const incompleteSchedules = schedules.filter(s => !s.sharepoint_folder_id || !s.sharepoint_folder_url)
    
    console.log('📊 SUMMARY:')
    console.log('============')
    console.log(`✅ Complete schedules: ${completeSchedules.length}`)
    console.log(`❌ Incomplete schedules: ${incompleteSchedules.length}`)
    
    if (incompleteSchedules.length > 0) {
      console.log('\n⚠️  Schedules missing SharePoint folders:')
      incompleteSchedules.forEach(s => {
        console.log(`   - ${s.custom_id} (${s.scheduled_date})`)
      })
      
      console.log('\n💡 To fix missing folders, you can:')
      console.log('   1. Manually trigger folder creation for a specific schedule:')
      console.log(`      curl -X POST http://localhost:3000/api/ensure-sharepoint-folder -H "Content-Type: application/json" -d '{"scheduleId":"${incompleteSchedules[0].id}"}'`)
      console.log('   2. Or trigger folder creation for all schedules:')
      console.log('      curl -X POST http://localhost:3000/api/ensure-sharepoint-folders')
    } else {
      console.log('\n🎉 All recent schedules have SharePoint folders!')
    }
    
  } catch (error) {
    console.error('❌ Error checking schedule folders:', error.message)
  }
}

checkScheduleFolders()
  .then(() => {
    console.log('\n🏁 Check completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Check failed:', error)
    process.exit(1)
  })
