const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'
)

async function createCompleteTestData() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  console.log('🔧 CREATING COMPLETE TEST DATA')
  console.log('==============================')
  
  try {
    // 1. Create test client
    console.log('\n1️⃣ CREATING TEST CLIENT:')
    
    const testClient = {
      name: 'Test Client Corp',
      email: '<EMAIL>',
      phone: '+91-9876543210',
      address: 'Test Address, Mumbai',
      client_type: 'corporate',
      has_gst: true,
      gst_number: 'GST123456789'
    }
    
    const { data: createdClient, error: clientError } = await supabase
      .from('clients')
      .insert(testClient)
      .select()
      .single()
    
    if (clientError) {
      console.error('❌ Client error:', clientError)
      return
    }
    
    console.log(`✅ Created client: ${createdClient.name} (${createdClient.id})`)
    
    // 2. Create test project
    console.log('\n2️⃣ CREATING TEST PROJECT:')
    
    const testProject = {
      name: 'Corporate Event Photography',
      client_id: createdClient.id,
      description: 'Annual corporate event photography project',
      location: 'Mumbai Convention Center',
      status: 'active',
      total_amount: 50000,
      gst_inclusive: true,
      amount_received: 0,
      amount_pending: 50000
    }
    
    const { data: createdProject, error: projectError } = await supabase
      .from('projects')
      .insert(testProject)
      .select()
      .single()
    
    if (projectError) {
      console.error('❌ Project error:', projectError)
      return
    }
    
    console.log(`✅ Created project: ${createdProject.name} (${createdProject.id})`)
    
    // 3. Create test schedules
    console.log('\n3️⃣ CREATING TEST SCHEDULES:')
    
    const testSchedules = [
      {
        project_id: createdProject.id,
        scheduled_date: '2024-01-15T10:00:00Z',
        scheduled_end_date: '2024-01-15T16:00:00Z',
        location: 'Mumbai Convention Center',
        status: 'completed',
        amount: 7000,
        is_outsourced: true,
        notes: 'Main event photography - Day 1'
      },
      {
        project_id: createdProject.id,
        scheduled_date: '2024-01-20T14:00:00Z',
        scheduled_end_date: '2024-01-20T18:00:00Z',
        location: 'Hotel Grand Ballroom',
        status: 'scheduled',
        amount: 4000,
        is_outsourced: true,
        notes: 'Awards ceremony photography - Day 2'
      }
    ]
    
    const { data: createdSchedules, error: schedulesError } = await supabase
      .from('schedules')
      .insert(testSchedules)
      .select()
    
    if (schedulesError) {
      console.error('❌ Schedules error:', schedulesError)
      return
    }
    
    console.log(`✅ Created ${createdSchedules.length} test schedules`)
    createdSchedules.forEach((schedule, index) => {
      console.log(`   ${index + 1}. ${schedule.notes} - ${schedule.scheduled_date}`)
    })
    
    // 4. Create schedule_vendors links
    console.log('\n4️⃣ CREATING SCHEDULE_VENDORS LINKS:')
    
    const scheduleVendorLinks = [
      {
        schedule_id: createdSchedules[0].id,
        vendor_id: vendorId,
        cost: 7000,
        notes: 'Photography services for main event'
      },
      {
        schedule_id: createdSchedules[1].id,
        vendor_id: vendorId,
        cost: 4000,
        notes: 'Photography services for awards ceremony'
      }
    ]
    
    const { data: createdLinks, error: linksError } = await supabase
      .from('schedule_vendors')
      .insert(scheduleVendorLinks)
      .select()
    
    if (linksError) {
      console.error('❌ Schedule vendors error:', linksError)
      return
    }
    
    console.log(`✅ Created ${createdLinks.length} schedule-vendor links`)
    createdLinks.forEach((link, index) => {
      console.log(`   ${index + 1}. Schedule: ${link.schedule_id}, Cost: ₹${link.cost}`)
    })
    
    // 5. Update project with vendor payment status
    console.log('\n5️⃣ UPDATING PROJECT PAYMENT STATUS:')
    
    const totalVendorCost = scheduleVendorLinks.reduce((sum, link) => sum + link.cost, 0)
    
    const { error: projectUpdateError } = await supabase
      .from('projects')
      .update({
        vendor_payment_status: 'pending',
        vendor_payment_amount: totalVendorCost,
        vendor_payment_due_date: '2024-02-15',
        updated_at: new Date().toISOString()
      })
      .eq('id', createdProject.id)
    
    if (projectUpdateError) {
      console.error('❌ Project update error:', projectUpdateError)
      return
    }
    
    console.log(`✅ Updated project with vendor payment status: ₹${totalVendorCost} pending`)
    
    // 6. Create corresponding expenses
    console.log('\n6️⃣ CREATING CORRESPONDING EXPENSES:')
    
    const testExpenses = scheduleVendorLinks.map((link, index) => ({
      project_id: createdProject.id,
      category: 'outsourcing',
      amount: link.cost,
      description: `Photography services - Arun photography (${link.notes})`,
      date: createdSchedules[index].scheduled_date.split('T')[0], // Extract date part
      user_id: '545522ca-f408-4006-bdf7-71bc909c67c6' // Valid user ID
    }))
    
    const { data: createdExpenses, error: expensesError } = await supabase
      .from('expenses')
      .insert(testExpenses)
      .select()
    
    if (expensesError) {
      console.error('❌ Expenses error:', expensesError)
      return
    }
    
    console.log(`✅ Created ${createdExpenses.length} expense records`)
    createdExpenses.forEach((expense, index) => {
      console.log(`   ${index + 1}. Amount: ₹${expense.amount}, Description: ${expense.description}`)
    })
    
    // 7. Test the API endpoint
    console.log('\n7️⃣ TESTING VENDOR-SHOOTS API:')
    
    // Wait a moment for data to be consistent
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const response = await fetch(`http://localhost:3002/api/vendor-shoots?vendorId=${vendorId}`)
    const apiData = await response.json()
    
    if (!response.ok) {
      console.error('❌ API error:', apiData)
      return
    }
    
    console.log(`✅ API returned ${apiData.shoots?.length || 0} shoots`)
    
    // Calculate totals
    const totalCost = apiData.shoots?.reduce((sum, shoot) => {
      const vendorEntry = shoot.vendors?.find(sv => sv.vendor_id === vendorId)
      return sum + (vendorEntry?.cost || 0)
    }, 0) || 0
    
    console.log('\n📊 SUMMARY:')
    console.log(`   Total vendor cost from API: ₹${totalCost}`)
    console.log(`   Expected total: ₹${totalVendorCost}`)
    console.log(`   Project payment status: pending`)
    console.log(`   Project payment amount: ₹${totalVendorCost}`)
    
    if (apiData.shoots?.length > 0) {
      console.log('\n📋 SHOOTS DETAILS:')
      apiData.shoots.forEach((shoot, index) => {
        const vendorEntry = shoot.vendors?.find(sv => sv.vendor_id === vendorId)
        console.log(`   ${index + 1}. ${shoot.project?.name} - ₹${vendorEntry?.cost || 0}`)
        console.log(`      Status: ${shoot.project?.vendor_payment_status || 'Not set'}`)
        console.log(`      Date: ${shoot.scheduled_date}`)
      })
    }
    
    console.log('\n✅ COMPLETE TEST DATA CREATED SUCCESSFULLY!')
    console.log('🔗 Now check the vendor details page at: http://localhost:3002/vendors/' + vendorId)
    console.log('📊 You should see:')
    console.log('   - Total Spent: ₹11,000')
    console.log('   - Total Pending: ₹11,000')
    console.log('   - 2 projects')
    console.log('   - Payment Status: ₹11,000 Pending')
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

createCompleteTestData()