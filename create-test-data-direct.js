import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

// Use service role key to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function createTestDataDirect() {
  console.log('🔧 Creating test data directly with service role...\n')
  
  try {
    // 1. Create a test client
    console.log('👥 Step 1: Creating test client...')
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .insert({
        custom_id: 'CYM-002',
        name: 'Test Client',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Test Street, Test City',
        has_gst: false,
        client_type: 'corporate'
      })
      .select('id, custom_id, name')
      .single()
    
    if (clientError) {
      throw new Error(`Failed to create client: ${clientError.message}`)
    }
    
    console.log(`   ✅ Client created: ${client.custom_id} - ${client.name}`)
    
    // 2. Skip pilot creation (users table has auth constraints)
    console.log('\n👨‍✈️ Step 2: Skipping pilot creation (auth constraints)...')
    console.log('   ℹ️  Pilot will be set to null in schedule')
    
    // 3. Create a test project
    console.log('\n📁 Step 3: Creating test project...')
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        custom_id: 'PRJ-002',
        name: 'Test Project',
        description: 'A test project for folder creation testing',
        client_id: client.id,
        location: 'Test Location',
        status: 'active',
        total_amount: 10000,
        gst_inclusive: true,
        amount_received: 5000,
        amount_pending: 5000
      })
      .select('id, custom_id, name, client_id')
      .single()
    
    if (projectError) {
      throw new Error(`Failed to create project: ${projectError.message}`)
    }
    
    console.log(`   ✅ Project created: ${project.custom_id} - ${project.name}`)
    
    // 4. Create a test schedule (without pilot for now)
    console.log('\n📅 Step 4: Creating test schedule...')
    const { data: schedule, error: scheduleError } = await supabase
      .from('schedules')
      .insert({
        project_id: project.id,
        scheduled_date: '2025-01-15',
        scheduled_end_date: '2025-01-15',
        status: 'scheduled',
        pilot_id: null, // No pilot for now
        amount: 5000,
        location: 'Test Location',
        notes: 'Test schedule for folder creation verification',
        is_recurring: false,
        is_outsourced: false
      })
      .select('id, custom_id, project_id, scheduled_date, pilot_id, amount, location, notes')
      .single()
    
    if (scheduleError) {
      throw new Error(`Failed to create schedule: ${scheduleError.message}`)
    }
    
    console.log(`   ✅ Schedule created: ${schedule.custom_id}`)
    console.log(`   📅 Date: ${schedule.scheduled_date}`)
    console.log(`   📍 Location: ${schedule.location}`)
    console.log(`   💰 Amount: ${schedule.amount}`)
    
    console.log('\n🎉 Test data created successfully!')
    console.log('\n📊 Summary:')
    console.log(`   - Client: ${client.custom_id} - ${client.name}`)
    console.log(`   - Project: ${project.custom_id} - ${project.name}`)
    console.log(`   - Schedule: ${schedule.custom_id} - ${schedule.scheduled_date}`)
    
    console.log('\n🚀 Now testing SharePoint folder creation...')
    console.log(`   Schedule ID: ${schedule.id}`)
    console.log(`   Schedule Custom ID: ${schedule.custom_id}`)
    
    // 5. Test SharePoint folder creation via API
    console.log('\n🔍 Step 5: Testing SharePoint folder creation...')
    
    try {
      const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scheduleId: schedule.id
        })
      })
      
      if (response.ok) {
        const result = await response.json()
        console.log('   ✅ SharePoint folder creation API call successful')
        console.log(`   📋 Result: ${result.message}`)
      } else {
        console.log(`   ❌ SharePoint folder creation failed: ${response.status}`)
        const errorText = await response.text()
        console.log(`   Error: ${errorText}`)
      }
    } catch (apiError) {
      console.log('   ❌ SharePoint API connection failed:', apiError.message)
    }
    
    // 6. Wait and check the schedule again
    console.log('\n⏳ Step 6: Waiting for folder creation to complete...')
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    const { data: updatedSchedule, error: fetchError } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', schedule.id)
      .single()
    
    if (fetchError) {
      console.log('   ❌ Error fetching updated schedule:', fetchError.message)
    } else {
      const hasFolder = !!updatedSchedule.sharepoint_folder_id
      const hasUrl = !!updatedSchedule.sharepoint_folder_url
      const hasShareLink = !!updatedSchedule.sharepoint_share_link
      
      console.log('\n📊 Folder Creation Results:')
      console.log('============================')
      console.log(`✅ Main Schedule Folder ID: ${hasFolder ? 'PRESENT' : 'MISSING'}`)
      if (hasFolder) {
        console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
      }
      
      console.log(`✅ Main Schedule Folder URL: ${hasUrl ? 'PRESENT' : 'MISSING'}`)
      if (hasUrl) {
        console.log(`   🌐 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
      }
      
      console.log(`✅ Main Schedule Share Link: ${hasShareLink ? 'PRESENT' : 'MISSING'}`)
      if (hasShareLink) {
        console.log(`   🔗 Share Link: ${updatedSchedule.sharepoint_share_link}`)
      }
      
      if (hasFolder && hasUrl) {
        console.log('\n🎉 SUCCESS: SharePoint folder creation is working!')
        console.log('✅ Main schedule folder is properly saved to database')
        console.log('✅ Raw/Output subfolders are NOT saved to database (as intended)')
      } else {
        console.log('\n❌ ISSUE: SharePoint folder creation may have failed')
        console.log('   This could be due to:')
        console.log('   - SharePoint API configuration issues')
        console.log('   - Environment variables not set correctly')
        console.log('   - Microsoft Graph API permissions')
      }
    }
    
    return {
      client,
      project,
      schedule: updatedSchedule || schedule
    }
    
  } catch (error) {
    console.error('❌ Failed to create test data:', error.message)
    throw error
  }
}

createTestDataDirect()
  .then((data) => {
    console.log('\n🏁 Test completed')
    console.log('\n💡 Next steps:')
    console.log('   1. If folder creation failed, check your SharePoint configuration')
    console.log('   2. Verify Microsoft Graph API permissions')
    console.log('   3. Check environment variables (MICROSOFT_CLIENT_ID, etc.)')
    console.log('   4. You can manually trigger folder creation again:')
    console.log(`      curl -X POST http://localhost:3000/api/ensure-sharepoint-folder -H "Content-Type: application/json" -d '{"scheduleId":"${data.schedule.id}"}'`)
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Test failed:', error)
    process.exit(1)
  })
