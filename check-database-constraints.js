import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

// Use service role key to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function checkDatabaseConstraints() {
  console.log('🔍 Checking database constraints...\n')
  
  try {
    // Check if there are any foreign key constraints on shoot_id
    console.log('   ℹ️  Checking constraints manually...')
    let constraints = null
    let constraintsError = null
    
    try {
      const result = await supabase.rpc('get_foreign_key_constraints', { table_name: 'tasks' })
      constraints = result.data
      constraintsError = result.error
    } catch (error) {
      console.log('   ℹ️  RPC not available, checking manually...')
    }
    
    if (constraintsError) {
      console.log('   ❌ Error checking constraints:', constraintsError.message)
    } else if (constraints) {
      console.log('   📋 Foreign key constraints on tasks table:')
      constraints.forEach(c => {
        console.log(`      - ${c.constraint_name}: ${c.column_name} -> ${c.foreign_table_name}.${c.foreign_column_name}`)
      })
    }
    
    // Check if tasks table has shoot_id column
    console.log('\n📋 Checking tasks table structure...')
    const { data: tasksSample, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title, shoot_id, project_id')
      .limit(1)
    
    if (tasksError) {
      console.log('   ❌ Error querying tasks:', tasksError.message)
    } else {
      console.log('   ✅ Tasks table is accessible')
      if (tasksSample && tasksSample.length > 0) {
        console.log('   📋 Sample task structure:', tasksSample[0])
      }
    }
    
    // Check if there are any tasks with shoot_id values
    console.log('\n📊 Checking tasks with shoot_id...')
    const { data: tasksWithShootId, error: shootTasksError } = await supabase
      .from('tasks')
      .select('id, title, shoot_id')
      .not('shoot_id', 'is', null)
      .limit(5)
    
    if (shootTasksError) {
      console.log('   ❌ Error querying tasks with shoot_id:', shootTasksError.message)
    } else {
      console.log(`   📋 Found ${tasksWithShootId?.length || 0} tasks with shoot_id:`)
      if (tasksWithShootId && tasksWithShootId.length > 0) {
        tasksWithShootId.forEach(task => {
          console.log(`      - ${task.title} (shoot_id: ${task.shoot_id})`)
        })
      }
    }
    
    // Check if the shoot_id values reference existing schedules
    console.log('\n🔗 Checking shoot_id references...')
    if (tasksWithShootId && tasksWithShootId.length > 0) {
      for (const task of tasksWithShootId) {
        const { data: schedule, error: scheduleError } = await supabase
          .from('schedules')
          .select('id, custom_id, scheduled_date')
          .eq('id', task.shoot_id)
          .single()
        
        if (scheduleError) {
          console.log(`   ❌ Task ${task.title}: shoot_id ${task.shoot_id} -> No matching schedule found`)
        } else {
          console.log(`   ✅ Task ${task.title}: shoot_id ${task.shoot_id} -> Schedule ${schedule.custom_id}`)
        }
      }
    }
    
    // Check recent schedules to see if they have tasks
    console.log('\n📅 Checking recent schedules for tasks...')
    const { data: recentSchedules, error: recentSchedulesError } = await supabase
      .from('schedules')
      .select('id, custom_id, scheduled_date')
      .order('created_at', { ascending: false })
      .limit(3)
    
    if (recentSchedulesError) {
      console.log('   ❌ Error querying recent schedules:', recentSchedulesError.message)
    } else if (recentSchedules && recentSchedules.length > 0) {
      for (const schedule of recentSchedules) {
        const { data: scheduleTasks, error: scheduleTasksError } = await supabase
          .from('tasks')
          .select('id, title, shoot_id')
          .eq('shoot_id', schedule.id)
        
        if (scheduleTasksError) {
          console.log(`   ❌ Error querying tasks for schedule ${schedule.custom_id}:`, scheduleTasksError.message)
        } else {
          console.log(`   📋 Schedule ${schedule.custom_id}: ${scheduleTasks?.length || 0} tasks`)
          if (scheduleTasks && scheduleTasks.length > 0) {
            scheduleTasks.forEach(task => {
              console.log(`      - ${task.title}`)
            })
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking constraints:', error.message)
  }
}

checkDatabaseConstraints()
  .then(() => {
    console.log('\n🏁 Check completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Check failed:', error)
    process.exit(1)
  })
