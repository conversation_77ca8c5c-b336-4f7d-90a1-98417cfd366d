require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function debugScheduleDates() {
  console.log('🔍 Debugging schedule date fields...');
  
  // Get the exact same data that SharePointService.ensureScheduleFolder would get
  const { data: schedule, error } = await supabase
    .from('schedules')
    .select(`
      id,
      custom_id,
      project_id,
      scheduled_date,
      scheduled_end_date,
      sharepoint_folder_url,
      projects!inner (
        id,
        custom_id,
        name,
        client_id,
        clients!inner (
          id,
          custom_id,
          name,
          sharepoint_folder_id
        )
      )
    `)
    .eq('custom_id', 'CYM-25-058')
    .single();
    
  if (error) {
    console.error('Error:', error);
    return;
  }
  
  console.log('\n📅 Schedule Data (as retrieved by SharePointService):');
  console.log('   Custom ID:', schedule.custom_id);
  console.log('   scheduled_date (raw):', schedule.scheduled_date);
  console.log('   scheduled_end_date (raw):', schedule.scheduled_end_date);
  
  // Format the date exactly as SharePointService does
  const scheduleDate = new Date(schedule.scheduled_date);
  const formattedDate = `${scheduleDate.getFullYear()}-${String(scheduleDate.getMonth() + 1).padStart(2, '0')}-${String(scheduleDate.getDate()).padStart(2, '0')}`;
  
  console.log('   Formatted scheduled_date:', formattedDate);
  console.log('   SharePoint folder URL:', schedule.sharepoint_folder_url);
  
  // Extract date from SharePoint URL
  const urlMatch = schedule.sharepoint_folder_url.match(/(\d{4}-\d{2}-\d{2})/);
  if (urlMatch) {
    const folderDate = urlMatch[1];
    console.log('   Date in folder URL:', folderDate);
    console.log('   Does formatted scheduled_date match folder?', formattedDate === folderDate);
  }
}

debugScheduleDates().catch(console.error);