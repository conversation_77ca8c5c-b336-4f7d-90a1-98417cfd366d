require('dotenv').config({ path: '.env.local' });

async function testFrontendScheduleCreation() {
  try {
    console.log('🧪 Testing Frontend Schedule Creation Flow...\n');
    
    const { createClient } = require('@supabase/supabase-js');
    
    // Use service role key to bypass RLS for testing
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    // Step 1: Get a project and user for testing
    console.log('1️⃣ Finding test data...');
    
    const { data: projects } = await supabase
      .from('projects')
      .select('id, custom_id, name, client:clients(name, client_type)')
      .limit(1);
    
    if (!projects || projects.length === 0) {
      throw new Error('No projects found');
    }
    
    const project = projects[0];
    console.log(`   ✅ Using project: ${project.name} (${project.custom_id})`);
    
    // Step 2: Call the create_schedule_with_vendors_v3 RPC function directly
    console.log('\n2️⃣ Creating schedule via RPC function (frontend flow)...');
    
    const scheduleData = {
      p_project_id: project.id,
      p_scheduled_date: new Date().toISOString(),
      p_scheduled_end_date: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
      p_pilot_id: null,
      p_amount: 2000,
      p_location: 'Test Location Frontend',
      p_google_maps_link: null,
      p_notes: 'Test schedule created via frontend flow simulation',
      p_is_recurring: false,
      p_recurring_pattern: null,
      p_is_outsourced: false,
      p_vendors: []
    };
    
    const { data: schedule, error: createError } = await supabase.rpc('create_schedule_with_vendors_v3', scheduleData);
    
    if (createError) {
      throw new Error(`Failed to create schedule: ${createError.message}`);
    }
    
    console.log(`   ✅ Schedule created: ${schedule.custom_id}`);
    console.log(`   🆔 Schedule ID: ${schedule.id}`);
    
    // Step 3: Trigger background job via API endpoint (simulating what happens in createWithVendors)
    console.log('\n3️⃣ Triggering background job via API...');
    
    try {
      // Call the background jobs API to queue SharePoint folder creation
      const response = await fetch('http://localhost:3002/api/background-jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'sharepoint_folder_creation',
          entityType: 'schedule',
          entityId: schedule.id,
          data: {
            scheduleId: schedule.id,
            customId: schedule.custom_id,
            scheduledDate: scheduleData.p_scheduled_date
          }
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`   ✅ Background job queued via API: ${result.jobId || 'success'}`);
      } else {
        console.log(`   ⚠️ API response: ${response.status} ${response.statusText}`);
      }
      
      // Wait a bit for the job to process
      console.log('\n4️⃣ Waiting for background job to process...');
      await new Promise(resolve => setTimeout(resolve, 8000));
      
      // Check if SharePoint folder was created
      console.log('\n5️⃣ Checking SharePoint folder creation...');
      
      const { data: updatedSchedule } = await supabase
        .from('schedules')
        .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
        .eq('id', schedule.id)
        .single();
      
      if (updatedSchedule.sharepoint_folder_id && updatedSchedule.sharepoint_folder_url) {
        console.log('   ✅ SharePoint folder created successfully!');
        console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`);
        console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`);
        if (updatedSchedule.sharepoint_share_link) {
          console.log(`   🔗 Share Link: ${updatedSchedule.sharepoint_share_link}`);
        }
      } else {
        console.log('   ⚠️ SharePoint folder not created yet or failed');
        console.log('   📊 Current schedule state:', {
          sharepoint_folder_id: updatedSchedule.sharepoint_folder_id,
          sharepoint_folder_url: updatedSchedule.sharepoint_folder_url,
          sharepoint_share_link: updatedSchedule.sharepoint_share_link
        });
      }
      
    } catch (bgError) {
      console.error('   ❌ Background job error:', bgError.message);
    }
    
    console.log('\n✅ Frontend schedule creation test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testFrontendScheduleCreation();