const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'

console.log('🔗 Connecting to Supabase...')
const supabase = createClient(supabaseUrl, supabaseKey)

async function updateVendorPaymentData() {
  console.log('🔍 Finding Arun photography vendor...')
  
  // Find Arun photography vendor
  const { data: vendor, error: vendorError } = await supabase
    .from('outsourcing_vendors')
    .select('*')
    .eq('name', 'Arun photography')
    .single()
    
  if (vendorError || !vendor) {
    console.error('❌ Vendor not found:', vendorError)
    return
  }
  
  console.log('✅ Found vendor:', vendor.name, '(ID:', vendor.id + ')')
  
  // Find the latest schedule
  console.log('🔍 Finding latest schedule...')
  const { data: schedule, error: scheduleError } = await supabase
    .from('schedules')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(1)
    .single()
    
  if (scheduleError || !schedule) {
    console.error('❌ Schedule not found:', scheduleError)
    return
  }
  
  console.log('✅ Found schedule:', schedule.id, 'for project:', schedule.project_id)
  
  // Update schedule_vendors cost
  console.log('💰 Updating schedule_vendors cost...')
  const { data: updatedLink, error: updateLinkError } = await supabase
    .from('schedule_vendors')
    .update({
      cost: 4000,
      notes: 'Photography services by Arun photography'
    })
    .eq('schedule_id', schedule.id)
    .eq('vendor_id', vendor.id)
    .select()
    .single()
    
  if (updateLinkError) {
    console.error('❌ Error updating schedule_vendors:', updateLinkError)
    return
  }
  
  console.log('✅ Schedule_vendors updated:', updatedLink)
  
  // Update project vendor payment information
  console.log('🏗️ Updating project vendor payment info...')
  const { data: updatedProject, error: updateProjectError } = await supabase
    .from('projects')
    .update({
      vendor_payment_status: 'pending',
      vendor_payment_amount: 4000,
      vendor_payment_notes: 'Payment for Arun photography services'
    })
    .eq('id', schedule.project_id)
    .select()
    .single()
    
  if (updateProjectError) {
    console.error('❌ Error updating project:', updateProjectError)
    return
  }
  
  console.log('✅ Project updated:', updatedProject.name)
  
  // Test the API logic
  console.log('\n🧪 Testing vendor-shoots API logic...')
  
  const { data: scheduleVendors, error: svError } = await supabase
    .from('schedule_vendors')
    .select(`
      schedule_id,
      cost,
      notes,
      schedules!inner (
        id,
        project_id,
        scheduled_date,
        amount,
        location,
        status,
        projects!inner (
          id,
          name,
          client_id,
          vendor_payment_status,
          vendor_payment_amount,
          clients!inner (
            id,
            name
          )
        )
      )
    `)
    .eq('vendor_id', vendor.id)
    
  if (svError) {
    console.error('❌ Error fetching schedule vendors:', svError)
    return
  }
  
  console.log('📋 Schedule Vendors Data:', JSON.stringify(scheduleVendors, null, 2))
  
  // Calculate totals like the frontend does
  let totalPaid = 0
  let totalPending = 0
  let totalProjects = 0
  
  scheduleVendors.forEach(sv => {
    const project = sv.schedules.projects
    const paymentStatus = project.vendor_payment_status
    const paymentAmount = project.vendor_payment_amount || sv.cost || 0
    
    totalProjects++
    
    if (paymentStatus === 'paid') {
      totalPaid += paymentAmount
    } else {
      totalPending += paymentAmount
    }
  })
  
  console.log('\n💰 Payment Summary:')
  console.log('Total Paid: ₹' + totalPaid)
  console.log('Total Pending: ₹' + totalPending)
  console.log('Total Projects:', totalProjects)
  console.log('Avg Cost/Project: ₹' + (totalProjects > 0 ? (totalPaid + totalPending) / totalProjects : 0))
  
  console.log('\n🎉 Vendor payment data updated successfully!')
  console.log('📝 The vendor details page should now show:')
  console.log('- Total Projects: 1')
  console.log('- Total Spent: ₹4,000')
  console.log('- Avg Cost/Project: ₹4,000')
  console.log('- Active Projects: 1')
  console.log('- Payment Status: ₹4,000 Pending')
}

updateVendorPaymentData().catch(console.error)