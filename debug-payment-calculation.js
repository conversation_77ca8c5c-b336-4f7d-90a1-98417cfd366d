const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'
)

async function debugPaymentCalculation() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  console.log('🔍 DEBUGGING PAYMENT CALCULATION')
  console.log('================================')
  
  // 1. Check schedule_vendors links
  console.log('\n1️⃣ SCHEDULE_VENDORS LINKS:')
  const { data: scheduleVendors, error: svError } = await supabase
    .from('schedule_vendors')
    .select(`
      *,
      schedules!inner (
        id,
        scheduled_date,
        project_id,
        notes
      )
    `)
    .eq('vendor_id', vendorId)
    
  if (svError) {
    console.error('❌ Error fetching schedule_vendors:', svError)
    return
  }
  
  console.log(`✅ Found ${scheduleVendors.length} schedule-vendor links:`)
  scheduleVendors.forEach((sv, index) => {
    console.log(`   ${index + 1}. Schedule: ${sv.schedules?.notes || 'No notes'} - Cost: ₹${sv.cost}`)
    console.log(`      Project ID: ${sv.schedules?.project_id}`)
    console.log(`      Schedule ID: ${sv.schedules?.id}`)
    console.log(`      Date: ${sv.schedules?.scheduled_date}`)
  })
  
  // 2. Check expenses
  console.log('\n2️⃣ EXPENSES:')
  const { data: expenses, error: expError } = await supabase
    .from('expenses')
    .select('*')
    .eq('vendor_id', vendorId)
    
  if (expError) {
    console.error('❌ Error fetching expenses:', expError)
    return
  }
  
  console.log(`✅ Found ${expenses.length} expenses:`)
  expenses.forEach((exp, index) => {
    console.log(`   ${index + 1}. Amount: ₹${exp.amount} - ${exp.description}`)
    console.log(`      Date: ${exp.date}`)
  })
  
  // 3. Calculate totals
  console.log('\n3️⃣ CALCULATIONS:')
  const totalScheduleCosts = scheduleVendors.reduce((sum, sv) => sum + (sv.cost || 0), 0)
  const totalExpenses = expenses.reduce((sum, exp) => sum + (exp.amount || 0), 0)
  const pendingAmount = totalScheduleCosts - totalExpenses
  
  console.log(`📊 Total Schedule Costs: ₹${totalScheduleCosts}`)
  console.log(`💰 Total Expenses: ₹${totalExpenses}`)
  console.log(`⏳ Pending Amount: ₹${pendingAmount}`)
  
  // 4. Check API endpoint calculation
  console.log('\n4️⃣ API ENDPOINT TEST:')
  try {
    const response = await fetch(`http://localhost:3002/api/vendor-shoots?vendorId=${vendorId}`)
    const apiData = await response.json()
    console.log('✅ API Response:', JSON.stringify(apiData, null, 2))
  } catch (error) {
    console.error('❌ API Error:', error.message)
  }
}

debugPaymentCalculation().catch(console.error)