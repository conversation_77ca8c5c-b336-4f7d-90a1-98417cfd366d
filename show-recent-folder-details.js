// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

async function showRecentFolderDetails() {
  try {
    console.log('📁 Showing SharePoint folder details for recent schedules...')
    
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    // Get the most recent schedules with SharePoint folder info
    const { data: schedules, error } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        scheduled_date,
        created_at,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link,
        projects!inner (
          id,
          custom_id,
          name,
          clients!inner (
            id,
            custom_id,
            name
          )
        )
      `)
      .order('created_at', { ascending: false })
      .limit(5)
    
    if (error) {
      console.error('❌ Error fetching schedules:', error)
      return
    }
    
    console.log(`📊 Showing details for ${schedules.length} most recent schedules:\n`)
    
    for (const schedule of schedules) {
      const project = schedule.projects
      const client = project.clients
      
      console.log(`🎬 Schedule: ${schedule.custom_id}`)
      console.log(`   📅 Scheduled Date: ${schedule.scheduled_date}`)
      console.log(`   🕐 Created: ${new Date(schedule.created_at).toLocaleString()}`)
      console.log(`   🏢 Client: ${client.custom_id} ${client.name}`)
      console.log(`   📁 Project: ${project.custom_id} ${project.name}`)
      
      if (schedule.sharepoint_folder_id) {
        console.log(`   ✅ SharePoint Folder ID: ${schedule.sharepoint_folder_id}`)
        console.log(`   🔗 SharePoint URL: ${schedule.sharepoint_folder_url || 'Not set'}`)
        console.log(`   🌐 Share Link: ${schedule.sharepoint_share_link || 'Not set'}`)
      } else {
        console.log(`   ❌ No SharePoint folder information`)
      }
      
      console.log('')
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

showRecentFolderDetails()