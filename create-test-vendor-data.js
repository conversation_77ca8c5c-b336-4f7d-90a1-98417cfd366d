const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU'
)

async function createTestVendorData() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  console.log('🔧 CREATING TEST VENDOR DATA')
  console.log('============================')
  
  try {
    // 1. First, let's check if we have any clients and projects to work with
    console.log('\n1️⃣ CHECKING EXISTING DATA:')
    
    const { data: clients, error: clientsError } = await supabase
      .from('clients')
      .select('*')
      .limit(3)
    
    if (clientsError) {
      console.error('❌ Clients error:', clientsError)
      return
    }
    
    console.log(`✅ Found ${clients.length} clients`)
    
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(3)
    
    if (projectsError) {
      console.error('❌ Projects error:', projectsError)
      return
    }
    
    console.log(`✅ Found ${projects.length} projects`)
    
    if (clients.length === 0 || projects.length === 0) {
      console.log('❌ Need at least 1 client and 1 project to create test data')
      return
    }
    
    // 2. Create test schedules
    console.log('\n2️⃣ CREATING TEST SCHEDULES:')
    
    const testSchedules = [
      {
        project_id: projects[0].id,
        scheduled_date: '2024-01-15',
        start_time: '10:00:00',
        end_time: '16:00:00',
        location: 'Test Location 1',
        status: 'completed',
        is_outsourced: true,
        notes: 'Test schedule for Arun photography'
      },
      {
        project_id: projects[0].id,
        scheduled_date: '2024-01-20',
        start_time: '14:00:00',
        end_time: '18:00:00',
        location: 'Test Location 2',
        status: 'scheduled',
        is_outsourced: true,
        notes: 'Another test schedule for Arun photography'
      }
    ]
    
    const { data: createdSchedules, error: schedulesError } = await supabase
      .from('schedules')
      .insert(testSchedules)
      .select()
    
    if (schedulesError) {
      console.error('❌ Schedules error:', schedulesError)
      return
    }
    
    console.log(`✅ Created ${createdSchedules.length} test schedules`)
    
    // 3. Create schedule_vendors links
    console.log('\n3️⃣ CREATING SCHEDULE_VENDORS LINKS:')
    
    const scheduleVendorLinks = createdSchedules.map((schedule, index) => ({
      schedule_id: schedule.id,
      vendor_id: vendorId,
      cost: index === 0 ? 7000 : 4000, // First one ₹7,000, second one ₹4,000
      notes: `Payment for ${schedule.notes}`
    }))
    
    const { data: createdLinks, error: linksError } = await supabase
      .from('schedule_vendors')
      .insert(scheduleVendorLinks)
      .select()
    
    if (linksError) {
      console.error('❌ Schedule vendors error:', linksError)
      return
    }
    
    console.log(`✅ Created ${createdLinks.length} schedule-vendor links`)
    createdLinks.forEach((link, index) => {
      console.log(`   ${index + 1}. Schedule: ${link.schedule_id}, Cost: ₹${link.cost}`)
    })
    
    // 4. Update project with vendor payment status
    console.log('\n4️⃣ UPDATING PROJECT PAYMENT STATUS:')
    
    const { error: projectUpdateError } = await supabase
      .from('projects')
      .update({
        vendor_payment_status: 'pending',
        vendor_payment_amount: 11000, // Total of both schedules
        vendor_payment_due_date: '2024-02-15',
        updated_at: new Date().toISOString()
      })
      .eq('id', projects[0].id)
    
    if (projectUpdateError) {
      console.error('❌ Project update error:', projectUpdateError)
      return
    }
    
    console.log('✅ Updated project with vendor payment status')
    
    // 5. Create corresponding expenses (optional - to show integration)
    console.log('\n5️⃣ CREATING CORRESPONDING EXPENSES:')
    
    const testExpenses = [
      {
        project_id: projects[0].id,
        category: 'outsourcing',
        amount: 7000,
        description: 'Photography services - Arun photography (Schedule 1)',
        expense_date: '2024-01-15',
        vendor_id: vendorId,
        payment_method: 'bank_transfer',
        status: 'pending'
      },
      {
        project_id: projects[0].id,
        category: 'outsourcing',
        amount: 4000,
        description: 'Photography services - Arun photography (Schedule 2)',
        expense_date: '2024-01-20',
        vendor_id: vendorId,
        payment_method: 'bank_transfer',
        status: 'pending'
      }
    ]
    
    const { data: createdExpenses, error: expensesError } = await supabase
      .from('expenses')
      .insert(testExpenses)
      .select()
    
    if (expensesError) {
      console.error('❌ Expenses error:', expensesError)
      return
    }
    
    console.log(`✅ Created ${createdExpenses.length} expense records`)
    createdExpenses.forEach((expense, index) => {
      console.log(`   ${index + 1}. Amount: ₹${expense.amount}, Description: ${expense.description}`)
    })
    
    // 6. Test the API endpoint
    console.log('\n6️⃣ TESTING VENDOR-SHOOTS API:')
    const response = await fetch(`http://localhost:3002/api/vendor-shoots?vendorId=${vendorId}`)
    const apiData = await response.json()
    
    if (!response.ok) {
      console.error('❌ API error:', apiData)
      return
    }
    
    console.log(`✅ API returned ${apiData.shoots?.length || 0} shoots`)
    
    // Calculate totals
    const totalCost = apiData.shoots?.reduce((sum, shoot) => {
      const vendorEntry = shoot.vendors?.find(sv => sv.vendor_id === vendorId)
      return sum + (vendorEntry?.cost || 0)
    }, 0) || 0
    
    console.log(`📊 Total vendor cost: ₹${totalCost}`)
    console.log(`📊 Expected: ₹11,000 (₹7,000 + ₹4,000)`)
    
    console.log('\n✅ TEST DATA CREATED SUCCESSFULLY!')
    console.log('Now check the vendor details page to see if it shows the correct amounts.')
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

createTestVendorData()