// Simple test to verify the API fix works
async function testAPIFix() {
  console.log('🧪 Testing SharePoint API fix...\n')

  try {
    console.log('🔄 Calling SharePoint API endpoint...')
    
    const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      console.log('✅ SharePoint API call successful!')
      console.log(`   Status: ${response.status}`)
      
      const responseText = await response.text()
      if (responseText) {
        console.log(`   Response: ${responseText}`)
      }
    } else {
      const errorText = await response.text()
      console.error(`❌ SharePoint API call failed: ${response.status}`)
      console.error(`   Error: ${errorText}`)
    }

    console.log('\n🎯 API Fix Test Complete!')
    console.log('💡 The fix replaces dynamic import with API call, which should work in client-side context.')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testAPIFix()