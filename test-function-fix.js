require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testFunctions() {
  console.log('🧪 Testing PostgreSQL functions with correct signatures...');
  
  // Get a valid project ID first
  const { data: projects, error: projectError } = await supabase
    .from('projects')
    .select('id')
    .limit(1);
  
  if (projectError || !projects || projects.length === 0) {
    console.error('❌ Could not get a valid project ID:', projectError);
    return;
  }
  
  const projectId = projects[0].id;
  console.log('📋 Using project ID:', projectId);
  
  // Test create_schedule_with_vendors_v3 (should fail - doesn't exist)
  console.log('\n1️⃣ Testing create_schedule_with_vendors_v3 (expected to fail)...');
  try {
    const { data: v3Data, error: v3Error } = await supabase.rpc('create_schedule_with_vendors_v3', {
      p_project_id: projectId,
      p_scheduled_date: '2025-01-15T10:00:00Z',
      p_scheduled_end_date: '2025-01-15T18:00:00Z',
      p_pilot_id: null,
      p_amount: 1000,
      p_location: 'Test Location',
      p_google_maps_link: null,
      p_notes: 'Test notes',
      p_is_recurring: false,
      p_recurring_pattern: null,
      p_is_outsourced: false,
      p_vendors: []
    });
    
    if (v3Error) {
      console.log('❌ create_schedule_with_vendors_v3 error (expected):', v3Error.message);
    } else {
      console.log('⚠️ create_schedule_with_vendors_v3 unexpectedly succeeded:', v3Data);
    }
  } catch (err) {
    console.log('❌ create_schedule_with_vendors_v3 exception (expected):', err.message);
  }
  
  // Test create_schedule_with_vendors (should work)
  console.log('\n2️⃣ Testing create_schedule_with_vendors (should work)...');
  try {
    const { data: regularData, error: regularError } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: projectId,
      p_scheduled_date: '2025-01-15T10:00:00Z',
      p_scheduled_end_date: '2025-01-15T18:00:00Z',
      p_pilot_id: null,
      p_amount: 1000,
      p_location: 'Test Location',
      p_google_maps_link: null,
      p_notes: 'Test notes',
      p_is_recurring: false,
      p_recurring_pattern: null,
      p_is_outsourced: false,
      p_vendors: []
    });
    
    if (regularError) {
      console.log('❌ create_schedule_with_vendors error:', regularError.message);
    } else {
      console.log('✅ create_schedule_with_vendors succeeded!');
      console.log('📋 Returned data:', JSON.stringify(regularData, null, 2));
      
      // Clean up - delete the test schedule
      if (regularData && regularData.id) {
        await supabase
          .from('schedules')
          .delete()
          .eq('id', regularData.id);
        console.log('🧹 Cleaned up test schedule');
      }
    }
  } catch (err) {
    console.log('❌ create_schedule_with_vendors exception:', err.message);
  }
}

testFunctions();