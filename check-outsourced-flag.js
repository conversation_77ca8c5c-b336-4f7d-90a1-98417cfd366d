const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'
)

async function checkOutsourcedFlag() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  console.log('🔍 CHECKING OUTSOURCED FLAG')
  console.log('===========================')
  
  // 1. Get schedule IDs linked to vendor
  const { data: scheduleVendors, error: svError } = await supabase
    .from('schedule_vendors')
    .select('schedule_id')
    .eq('vendor_id', vendorId)
    
  if (svError) {
    console.error('❌ Error:', svError)
    return
  }
  
  const scheduleIds = scheduleVendors?.map(sv => sv.schedule_id) || []
  console.log(`📋 Found ${scheduleIds.length} schedule IDs:`, scheduleIds)
  
  // 2. Check is_outsourced flag for these schedules
  const { data: schedules, error: schedError } = await supabase
    .from('schedules')
    .select('id, is_outsourced, scheduled_date, notes')
    .in('id', scheduleIds)
    
  if (schedError) {
    console.error('❌ Error:', schedError)
    return
  }
  
  console.log('\n📊 SCHEDULE OUTSOURCED STATUS:')
  schedules?.forEach(schedule => {
    console.log(`   Schedule ${schedule.id}: is_outsourced = ${schedule.is_outsourced}`)
    console.log(`   Date: ${schedule.scheduled_date}`)
    console.log(`   Notes: ${schedule.notes}`)
    console.log('')
  })
  
  const outsourcedCount = schedules?.filter(s => s.is_outsourced).length || 0
  const notOutsourcedCount = schedules?.filter(s => !s.is_outsourced).length || 0
  
  console.log(`✅ Outsourced: ${outsourcedCount}`)
  console.log(`❌ Not Outsourced: ${notOutsourcedCount}`)
  
  if (notOutsourcedCount > 0) {
    console.log('\n🔧 FIXING OUTSOURCED FLAG...')
    const { error: updateError } = await supabase
      .from('schedules')
      .update({ is_outsourced: true })
      .in('id', scheduleIds)
      
    if (updateError) {
      console.error('❌ Update error:', updateError)
    } else {
      console.log('✅ Updated all schedules to is_outsourced = true')
    }
  }
}

checkOutsourcedFlag().catch(console.error)