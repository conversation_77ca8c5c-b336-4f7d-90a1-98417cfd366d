import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)

async function checkDatabaseState() {
  console.log('🔍 Checking database state...\n')

  // Check clients
  const { data: clients, error: clientsError } = await supabase
    .from('clients')
    .select('id, custom_id, name')
    .limit(5)
  
  console.log('👥 Clients:')
  if (clientsError) {
    console.log('   ❌ Error:', clientsError.message)
  } else if (!clients || clients.length === 0) {
    console.log('   ❌ No clients found')
    } else {
    console.log(`   ✅ Found ${clients.length} clients`)
    clients.forEach(c => console.log(`      - ${c.custom_id}: ${c.name}`))
  }
  
  // Check projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
    .select('id, custom_id, name, client_id')
      .limit(5)

  console.log('\n📁 Projects:')
    if (projectsError) {
    console.log('   ❌ Error:', projectsError.message)
    } else if (!projects || projects.length === 0) {
    console.log('   ❌ No projects found')
  } else {
    console.log(`   ✅ Found ${projects.length} projects`)
    projects.forEach(p => console.log(`      - ${p.custom_id}: ${p.name}`))
  }
  
  // Check schedules
  const { data: schedules, error: schedulesError } = await supabase
    .from('schedules')
    .select('id, custom_id, project_id, scheduled_date')
    .limit(5)
  
  console.log('\n📅 Schedules:')
  if (schedulesError) {
    console.log('   ❌ Error:', schedulesError.message)
  } else if (!schedules || schedules.length === 0) {
    console.log('   ❌ No schedules found')
    } else {
    console.log(`   ✅ Found ${schedules.length} schedules`)
    schedules.forEach(s => console.log(`      - ${s.custom_id}: ${s.scheduled_date}`))
  }
  
  // Check users (pilots)
  const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, name, role')
      .eq('role', 'pilot')
      .limit(5)

  console.log('\n👨‍✈️ Pilots:')
  if (usersError) {
    console.log('   ❌ Error:', usersError.message)
  } else if (!users || users.length === 0) {
    console.log('   ❌ No pilots found')
    } else {
    console.log(`   ✅ Found ${users.length} pilots`)
    users.forEach(u => console.log(`      - ${u.name} (${u.role})`))
  }
  
  console.log('\n📊 Summary:')
  console.log(`   - Clients: ${clients?.length || 0}`)
  console.log(`   - Projects: ${projects?.length || 0}`)
  console.log(`   - Schedules: ${schedules?.length || 0}`)
  console.log(`   - Pilots: ${users?.length || 0}`)
  
  if (!clients || clients.length === 0) {
    console.log('\n⚠️  No clients found - you need to create a client first')
  }
  
  if (!projects || projects.length === 0) {
    console.log('\n⚠️  No projects found - you need to create a project first')
  }
  
  if (!users || users.length === 0) {
    console.log('\n⚠️  No pilots found - you need to create a pilot user first')
  }
  
  if (!schedules || schedules.length === 0) {
    console.log('\n⚠️  No schedules found - you need to create a schedule first')
  }
}

checkDatabaseState()
  .then(() => {
    console.log('\n🏁 Check completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Check failed:', error)
    process.exit(1)
  })