require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function testRegularScheduleCreation() {
  try {
    console.log('🧪 Testing regular schedule creation...');
    
    // Get a user for the pilot_id
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id')
      .limit(1);
    
    if (usersError || !users || users.length === 0) {
      throw new Error('No users found');
    }
    
    // Get a project for the project_id
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id')
      .limit(1);
    
    if (projectsError || !projects || projects.length === 0) {
      throw new Error('No projects found');
    }
    
    const scheduleData = {
      project_id: projects[0].id,
      pilot_id: users[0].id,
      scheduled_date: new Date().toISOString().split('T')[0],
      amount: 5000,
      location: 'Test Location',
      notes: 'Test schedule creation'
    };
    
    console.log('📝 Creating schedule with data:', scheduleData);
    
    // Call the API endpoint that uses schedulesApi.create
    const response = await fetch('http://localhost:3002/api/test-schedule-bg-jobs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(scheduleData)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    const responseData = await response.json();
    console.log('✅ Schedule created successfully:', responseData.schedule);
    
    // Wait a bit for background job to process
    console.log('⏳ Waiting 10 seconds for background job to process...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Check if the schedule has a SharePoint folder link
    const { data: updatedSchedule, error: fetchError } = await supabase
      .from('schedules')
      .select('id, custom_id, onedrive_folder_path')
      .eq('id', responseData.schedule.id)
      .single();
    
    if (fetchError) {
      throw new Error(`Failed to fetch updated schedule: ${fetchError.message}`);
    }
    
    console.log('📁 Updated schedule with folder info:', updatedSchedule);
    
    if (updatedSchedule.onedrive_folder_path) {
      console.log('✅ SUCCESS: SharePoint folder was created!');
      console.log('📂 Folder path:', updatedSchedule.onedrive_folder_path);
    } else {
      console.log('❌ FAILURE: SharePoint folder was NOT created');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testRegularScheduleCreation();