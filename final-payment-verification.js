const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'
)

async function finalVerification() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1'
  
  console.log('🎯 FINAL PAYMENT VERIFICATION')
  console.log('============================')
  
  // Test the API endpoint
  try {
    const response = await fetch(`http://localhost:3001/api/vendor-shoots?vendorId=${vendorId}`)
    const apiData = await response.json()
    
    console.log(`✅ API returned ${apiData.shoots?.length || 0} shoots`)
    
    // Calculate payments like the frontend does
    const payments = apiData.shoots?.map(shoot => {
      const vendorEntry = shoot.vendors?.find(sv => sv.vendor_id === vendorId)
      return {
        outsourcing_cost: vendorEntry?.cost || 0,
        payment_status: shoot.project?.vendor_payment_status || 'pending',
        payment_amount: shoot.project?.vendor_payment_amount || (vendorEntry?.cost || 0),
        project_name: shoot.project?.name,
        schedule_date: shoot.scheduled_date
      }
    }) || []
    
    let totalPaid = 0
    let totalPending = 0
    let paidProjects = 0
    let pendingProjects = 0
    
    console.log('\n📊 PAYMENT BREAKDOWN:')
    payments.forEach((payment, index) => {
      console.log(`\n   ${index + 1}. ${payment.project_name}`)
      console.log(`      Cost: ₹${payment.outsourcing_cost}`)
      console.log(`      Status: ${payment.payment_status}`)
      console.log(`      Payment Amount: ₹${payment.payment_amount}`)
      
      const cost = payment.outsourcing_cost || 0
      const paymentAmount = payment.payment_amount || cost

      switch (payment.payment_status) {
        case 'paid':
          totalPaid += paymentAmount
          paidProjects += 1
          console.log(`      ✅ PAID: ₹${paymentAmount}`)
          break
        case 'pending':
        case 'overdue':
        default:
          totalPending += cost
          pendingProjects += 1
          console.log(`      ⏳ PENDING: ₹${cost}`)
          break
      }
    })
    
    console.log('\n🎯 EXPECTED UI VALUES:')
    console.log(`💰 Total Paid: ₹${totalPaid} (${paidProjects} projects)`)
    console.log(`⏳ Total Pending: ₹${totalPending} (${pendingProjects} projects)`)
    console.log(`📊 Total Projects: ${payments.length}`)
    console.log(`💵 Total Spent: ₹${totalPaid + totalPending}`)
    
    // Calculate other stats
    const totalCost = payments.reduce((sum, p) => sum + (p.outsourcing_cost || 0), 0)
    const avgCostPerProject = payments.length > 0 ? Math.round(totalCost / payments.length) : 0
    const activeProjects = payments.length // All projects with schedules are considered active
    
    console.log(`📈 Avg Cost/Project: ₹${avgCostPerProject}`)
    console.log(`🔥 Active Projects: ${activeProjects}`)
    
    console.log('\n✅ Payment details are now correctly calculated and should update in the UI!')
    
  } catch (error) {
    console.error('❌ API Error:', error.message)
  }
}

finalVerification().catch(console.error)