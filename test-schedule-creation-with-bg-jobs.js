require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testScheduleCreationWithBackgroundJobs() {
  console.log('🧪 Testing Schedule Creation with Background Jobs...\n')

  try {
    // 1. Find a valid project and client
    console.log('1️⃣ Finding a valid project and client...')
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select(`
        id, custom_id, name,
        client:clients(id, custom_id, name, client_type)
      `)
      .limit(1)

    if (projectError || !projects || projects.length === 0) {
      throw new Error('No projects found')
    }

    const project = projects[0]
    console.log(`   ✅ Using project: ${project.custom_id} ${project.name}`)
    console.log(`   ✅ Client: ${project.client.custom_id} ${project.client.name}`)

    // 2. Find a valid pilot (or any user)
    console.log('\n2️⃣ Finding a valid pilot...')
    const { data: pilots, error: pilotError } = await supabase
      .from('users')
      .select('id, name, role')
      .limit(1)

    if (pilotError || !pilots || pilots.length === 0) {
      throw new Error('No users found')
    }

    const pilot = pilots[0]
    console.log(`   ✅ Using user: ${pilot.name} (${pilot.role})`)

    // 3. Create schedule using the API (which should trigger background job)
    console.log('\n3️⃣ Creating schedule via API (should trigger background job)...')
    
    // Use the test-schedule-bg-jobs endpoint which uses schedulesApi.create
    const createResponse = await fetch('http://localhost:3001/api/test-schedule-bg-jobs', {
      method: 'GET' // This endpoint is GET and creates a test schedule
    })

    if (!createResponse.ok) {
      throw new Error(`Failed to create schedule: ${createResponse.status} ${createResponse.statusText}`)
    }

    const responseData = await createResponse.json()
    const createdSchedule = responseData.schedule
    console.log(`   ✅ Schedule created: ${createdSchedule.custom_id}`)
    console.log(`   📅 Scheduled for: ${createdSchedule.scheduled_date}`)
    console.log(`   🆔 Schedule ID: ${createdSchedule.id}`)

    // 4. Check background jobs immediately
    console.log('\n4️⃣ Checking background jobs immediately...')
    const jobsResponse = await fetch('http://localhost:3001/api/background-jobs')
    const jobsData = await jobsResponse.json()
    
    console.log(`   📋 Found ${jobsData.jobs.length} background jobs`)
    if (jobsData.jobs.length > 0) {
      jobsData.jobs.forEach(job => {
        console.log(`   🔄 Job: ${job.id}`)
        console.log(`      Type: ${job.type}`)
        console.log(`      Entity: ${job.entityType} (${job.entityId})`)
        console.log(`      Status: ${job.status}`)
        console.log(`      Attempts: ${job.attempts}/${job.maxAttempts}`)
        if (job.error) {
          console.log(`      Error: ${job.error}`)
        }
      })
    }

    // 5. Wait a bit and check again
    console.log('\n5️⃣ Waiting 5 seconds for background job processing...')
    await new Promise(resolve => setTimeout(resolve, 5000))

    const jobsResponse2 = await fetch('http://localhost:3001/api/background-jobs')
    const jobsData2 = await jobsResponse2.json()
    
    console.log(`   📋 Found ${jobsData2.jobs.length} background jobs after waiting`)
    if (jobsData2.jobs.length > 0) {
      jobsData2.jobs.forEach(job => {
        console.log(`   🔄 Job: ${job.id}`)
        console.log(`      Status: ${job.status}`)
        console.log(`      Attempts: ${job.attempts}/${job.maxAttempts}`)
        if (job.error) {
          console.log(`      Error: ${job.error}`)
        }
      })
    }

    // 6. Check if SharePoint folder was created
    console.log('\n6️⃣ Checking SharePoint folder creation...')
    const { data: updatedSchedule, error: fetchError } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', createdSchedule.id)
      .single()

    if (fetchError) {
      throw new Error(`Failed to fetch updated schedule: ${fetchError.message}`)
    }

    if (updatedSchedule.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created!')
      console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
      console.log(`   🌐 Share Link: ${updatedSchedule.sharepoint_share_link}`)
    } else {
      console.log('   ❌ SharePoint folder NOT created')
      console.log('   📝 This indicates the background job may have failed')
    }

    console.log('\n🎯 Test Complete!')
    console.log(`\n💡 You can clean up by deleting schedule ${createdSchedule.custom_id} if needed.`)

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

testScheduleCreationWithBackgroundJobs()