const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU'

console.log('🔗 Connecting to Supabase...')
const supabase = createClient(supabaseUrl, supabaseKey)

async function fixVendorPaymentIssue() {
  console.log('🔧 Fixing vendor payment issue for Arun photography...')
  
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  // Option 1: Create a sample project and schedule to demonstrate the system
  console.log('\n📋 Option 1: Create sample data to demonstrate the system')
  console.log('This would create:')
  console.log('- A sample project')
  console.log('- A schedule linked to the project')
  console.log('- Link the vendor to the schedule with ₹4,000 cost')
  console.log('- Mark the schedule as outsourced')
  
  // Option 2: Record the payment as an expense
  console.log('\n💰 Option 2: Record the ₹4,000 as an expense')
  console.log('This would create an expense record with:')
  console.log('- Amount: ₹4,000')
  console.log('- Category: outsourcing')
  console.log('- Vendor ID: linked to Arun photography')
  console.log('- Description: Photography services')
  
  // Option 3: Clear any cached/stale data
  console.log('\n🧹 Option 3: Clear any potential data inconsistencies')
  console.log('This would:')
  console.log('- Check for any orphaned data')
  console.log('- Clear browser cache recommendations')
  console.log('- Verify all calculations are correct')
  
  console.log('\n❓ Which option would you like to proceed with?')
  console.log('1. Create sample project and schedule (demonstrates full workflow)')
  console.log('2. Record as expense only (simpler approach)')
  console.log('3. Just verify and clean up (diagnostic only)')
  
  // For now, let's implement option 2 as it's the most practical
  console.log('\n🚀 Implementing Option 2: Recording as expense...')
  
  // First, let's check if we need a project to link the expense to
  const { data: projects, error: projectsError } = await supabase
    .from('projects')
    .select('id, name')
    .limit(1)
    
  if (projectsError) {
    console.error('❌ Error fetching projects:', projectsError)
    return
  }
  
  if (!projects || projects.length === 0) {
    console.log('⚠️ No projects found. You need to create a project first.')
    console.log('Please create a project in the UI, then run this script again.')
    return
  }
  
  const sampleProject = projects[0]
  console.log(`📁 Using project: ${sampleProject.name} (${sampleProject.id})`)
  
  // Create the expense record
  const expenseData = {
    project_id: sampleProject.id,
    category: 'outsourcing',
    description: 'Photography services by Arun photography',
    amount: 4000,
    date: new Date().toISOString().split('T')[0], // Today's date
    vendor_id: vendorId
  }
  
  console.log('\n💾 Creating expense record...')
  console.log('Expense data:', expenseData)
  
  const { data: expense, error: expenseError } = await supabase
    .from('expenses')
    .insert(expenseData)
    .select()
    .single()
    
  if (expenseError) {
    console.error('❌ Error creating expense:', expenseError)
    return
  }
  
  console.log('✅ Expense created successfully!')
  console.log('Expense ID:', expense.id)
  
  // Now update the project's vendor payment status
  console.log('\n🔄 Updating project vendor payment status...')
  
  const { data: updatedProject, error: updateError } = await supabase
    .from('projects')
    .update({
      vendor_payment_status: 'pending',
      vendor_payment_amount: 4000
    })
    .eq('id', sampleProject.id)
    .select()
    .single()
    
  if (updateError) {
    console.error('❌ Error updating project:', updateError)
    return
  }
  
  console.log('✅ Project updated successfully!')
  console.log('Vendor payment status:', updatedProject.vendor_payment_status)
  console.log('Vendor payment amount:', updatedProject.vendor_payment_amount)
  
  console.log('\n🎉 Fix completed! The vendor details page should now show:')
  console.log('- ₹4,000 in expenses')
  console.log('- Proper vendor payment tracking')
  console.log('- Linked to a real project')
  
  console.log('\n📝 Next steps:')
  console.log('1. Refresh the vendor details page in your browser')
  console.log('2. Clear browser cache if needed')
  console.log('3. Verify the payment amounts are now correct')
  console.log('4. You can mark the payment as "paid" when the vendor is actually paid')
}

fixVendorPaymentIssue().catch(console.error)