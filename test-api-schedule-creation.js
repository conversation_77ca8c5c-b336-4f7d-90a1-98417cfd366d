// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

async function testAPIScheduleCreation() {
  try {
    console.log('🧪 Testing Schedule Creation via API (Background Jobs / Ensure Endpoint)...\n')
    
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    // Step 1: Get a valid project and client
    console.log('1️⃣ Finding a valid project and client...')
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        custom_id,
        name,
        clients!inner (
          id,
          custom_id,
          name
        )
      `)
      .limit(1)
    
    if (projectError || !projects || projects.length === 0) {
      console.log('❌ No projects found:', projectError)
      return
    }
    
    const project = projects[0]
    const client = project.clients
    console.log(`   ✅ Using project: ${project.custom_id} ${project.name}`)
    console.log(`   ✅ Client: ${client.custom_id} ${client.name}`)
    
    // Step 2: Get a valid pilot (optional)
    console.log('\n2️⃣ Finding a valid pilot...')
    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
    
    if (userError || !users || users.length === 0) {
      console.log('❌ No users found:', userError)
      return
    }
    
    const pilot = users[0]
    console.log(`   ✅ Using pilot: ${pilot.name}`)
    
    // Step 3: Create schedule directly via Supabase (avoid auth redirect on API route)
    console.log('\n3️⃣ Creating test schedule via Supabase insert...')

    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date().toISOString(),
      scheduled_end_date: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
      pilot_id: null, // or pilot.id if desired
      amount: 1500,
      location: 'Test Location Regular',
      google_maps_link: null,
      notes: 'Test schedule created via service key insert (script)',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }

    const { data: schedule, error: createError } = await supabase
      .from('schedules')
      .insert(scheduleData)
      .select('id, custom_id, project_id, scheduled_date, location, notes')
      .single()

    if (createError) {
      throw new Error(`Failed to create schedule: ${createError.message}`)
    }

    const newSchedule = schedule
    console.log(`   ✅ Schedule created: ${newSchedule.custom_id}`)
    console.log(`   📅 Scheduled for: ${newSchedule.scheduled_date}`)
    console.log(`   🆔 Schedule ID: ${newSchedule.id}`)
    
    // Step 4: Trigger SharePoint folder creation for this schedule
    console.log('\n4️⃣ Triggering SharePoint folder creation (specific schedule)...')
    try {
      const ensureResp = await fetch('http://localhost:3000/api/ensure-sharepoint-folder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ scheduleId: newSchedule.id })
      })

      if (ensureResp.ok) {
        const ensureJson = await ensureResp.json()
        console.log('   ✅ Ensure endpoint response:', ensureJson)
      } else {
        const errText = await ensureResp.text()
        console.log('   ❌ Ensure endpoint failed:', ensureResp.status, errText)
      }
    } catch (e) {
      console.log('   ❌ Error calling ensure endpoint:', e.message)
    }
    
    // Optional small wait, in case any async updates occur
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Step 5: Check if SharePoint folder was created
    console.log('\n5️⃣ Checking SharePoint folder creation...')
    const { data: scheduleCheck, error: checkError } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', newSchedule.id)
      .single()
    
    if (checkError) {
      console.log('❌ Error checking schedule:', checkError)
      return
    }
    
    if (scheduleCheck.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created!')
      console.log(`   📁 Folder ID: ${scheduleCheck.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${scheduleCheck.sharepoint_folder_url}`)
      console.log(`   🌐 Share Link: ${scheduleCheck.sharepoint_share_link || 'Not set'}`)
    } else {
      console.log('   ⚠️  SharePoint folder not created yet')
    }
    
    console.log('\n🎯 Test Complete!')
    console.log(`\n💡 You can clean up by deleting schedule ${newSchedule.custom_id} if needed.`)
    
  } catch (error) {
    console.error('❌ Test error:', error)
  }
}

testAPIScheduleCreation()