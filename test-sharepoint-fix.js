#!/usr/bin/env node

/**
 * Test script to verify SharePoint folder creation fix
 * This script tests the complete flow of schedule creation and SharePoint folder creation
 */

require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testSharePointFolderCreation() {
  console.log('🧪 Testing SharePoint Folder Creation Fix...\n')

  try {
    // 1. Create a test schedule
    console.log('1️⃣ Creating test schedule...')
    
    // First get a valid project
    const { data: projects } = await supabase
      .from('projects')
      .select('id, name')
      .limit(1)
    
    if (!projects || projects.length === 0) {
      throw new Error('No projects found')
    }

    // Create schedule directly using service role to bypass RLS
    const scheduleData = {
      project_id: projects[0].id,
      scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      scheduled_end_date: new Date(Date.now() + 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000).toISOString(), // Tomorrow + 4 hours
      pilot_id: null, // Allow null pilot
      amount: 1000,
      location: 'Test Location',
      google_maps_link: null,
      notes: 'Test schedule for SharePoint folder creation',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }

    const { data: schedule, error: createError } = await supabase
      .from('schedules')
      .insert(scheduleData)
      .select()
      .single()
    
    if (createError) {
      throw new Error(`Failed to create schedule: ${createError.message}`)
    }
    
    // Queue SharePoint folder creation job
     console.log('   📋 Queueing SharePoint folder creation job...')
     
     // Use the test-sharepoint API endpoint to trigger folder creation for this specific schedule
     console.log('   🔄 Triggering SharePoint folder creation via API...')
     const testResponse = await fetch('http://localhost:3000/api/test-sharepoint', {
       method: 'POST',
       headers: {
         'Content-Type': 'application/json'
       },
       body: JSON.stringify({ scheduleId: schedule.id })
     })
     
     const responseText = await testResponse.text()
     console.log('   📄 API Response Status:', testResponse.status)
     console.log('   📄 API Response Text:', responseText.substring(0, 200) + '...')
     
     let testResult
     try {
       testResult = JSON.parse(responseText)
     } catch (parseError) {
       throw new Error(`Failed to parse API response as JSON. Status: ${testResponse.status}, Response: ${responseText.substring(0, 500)}`)
     }
     
     if (!testResponse.ok) {
       throw new Error(`API call failed: ${testResult.message || 'Unknown error'}`)
     }
     
     console.log('   ✅ SharePoint folder creation completed:', testResult.message)
     
     console.log(`   ✅ Schedule created: ${schedule.custom_id} (${schedule.id})`)

    // 2. Verify SharePoint folder was created
    console.log('\n2️⃣ Verifying SharePoint folder creation...')
    const { data: updatedSchedule, error: checkError } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', schedule.id)
      .single()
    
    if (checkError) {
      console.log(`   ❌ Could not verify schedule: ${checkError.message}`)
    } else if (updatedSchedule.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created successfully!')
      console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
      console.log(`   🌐 Share Link: ${updatedSchedule.sharepoint_share_link || 'Not set'}`)
      console.log('\n🎉 SharePoint folder creation fix verified successfully!')
    } else {
      console.log('   ❌ SharePoint folder was not created')
      console.log('   📊 API Result:', testResult)
    }

    // 3. Clean up - delete the test schedule
    console.log('\n3️⃣ Cleaning up test data...')
    const { error: deleteError } = await supabase
      .from('schedules')
      .delete()
      .eq('id', schedule.id)

    if (deleteError) {
      console.warn(`   ⚠️ Failed to delete test schedule: ${deleteError.message}`)
    } else {
      console.log('   ✅ Test schedule deleted')
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

// Run the test
testSharePointFolderCreation()