require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkFunctions() {
  try {
    console.log('🔍 Checking available functions...');
    
    // Query to find functions with 'schedule' in the name
    const { data, error } = await supabase
      .from('information_schema.routines')
      .select('routine_name, routine_type')
      .eq('routine_schema', 'public')
      .ilike('routine_name', '%schedule%');
    
    if (error) {
      console.log('❌ Error querying functions:', error.message);
      
      // Try a simpler approach - just test the functions directly
      console.log('\n🧪 Testing functions directly...');
      
      // Test v3 function
      console.log('Testing create_schedule_with_vendors_v3...');
      try {
        await supabase.rpc('create_schedule_with_vendors_v3', {});
      } catch (err) {
        if (err.message.includes('function') && err.message.includes('does not exist')) {
          console.log('❌ create_schedule_with_vendors_v3 does NOT exist');
        } else {
          console.log('✅ create_schedule_with_vendors_v3 exists (got different error)');
        }
      }
      
      // Test regular function
      console.log('Testing create_schedule_with_vendors...');
      try {
        await supabase.rpc('create_schedule_with_vendors', {});
      } catch (err) {
        if (err.message.includes('function') && err.message.includes('does not exist')) {
          console.log('❌ create_schedule_with_vendors does NOT exist');
        } else {
          console.log('✅ create_schedule_with_vendors exists (got different error)');
        }
      }
      
    } else {
      console.log('✅ Found functions:', data);
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error);
  }
}

checkFunctions();