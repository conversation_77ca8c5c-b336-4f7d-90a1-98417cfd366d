const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'
)

async function getValidUser() {
  console.log('🔍 Getting valid user ID...')
  
  const { data: users, error } = await supabase
    .from('users')
    .select('id, name, email')
    .limit(1)
    
  if (error) {
    console.error('❌ Error:', error)
    return
  }
  
  if (users && users.length > 0) {
    console.log('✅ Found user:', users[0])
    console.log('📋 User ID:', users[0].id)
  } else {
    console.log('📭 No users found')
  }
}

getValidUser().catch(console.error)