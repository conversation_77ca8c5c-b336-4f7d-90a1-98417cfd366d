// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

async function testNewScheduleCreation() {
  try {
    console.log('🧪 Testing New Schedule Creation with SharePoint Folder...\n')
    
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    // Step 1: Get a valid project and client
    console.log('1️⃣ Finding a valid project and client...')
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        custom_id,
        name,
        clients!inner (
          id,
          custom_id,
          name
        )
      `)
      .limit(1)
    
    if (projectError || !projects || projects.length === 0) {
      console.log('❌ No projects found:', projectError)
      return
    }
    
    const project = projects[0]
    const client = project.clients
    console.log(`   ✅ Using project: ${project.custom_id} ${project.name}`)
    console.log(`   ✅ Client: ${client.custom_id} ${client.name}`)
    
    // Step 2: Get a valid pilot
    console.log('\n2️⃣ Finding a valid pilot...')
    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
    
    if (userError || !users || users.length === 0) {
      console.log('❌ No users found:', userError)
      return
    }
    
    const pilot = users[0]
    console.log(`   ✅ Using pilot: ${pilot.name}`)
    
    // Step 3: Create a test schedule using the RPC function
    console.log('\n3️⃣ Creating test schedule...')
    const scheduleData = {
      p_project_id: project.id,
      p_scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      p_scheduled_end_date: new Date(Date.now() + 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000).toISOString(), // Tomorrow + 4 hours
      p_pilot_id: pilot.id,
      p_amount: 5000,
      p_location: 'Test Location for SharePoint',
      p_google_maps_link: 'https://maps.google.com',
      p_notes: 'Test schedule for SharePoint folder creation',
      p_is_recurring: false,
      p_recurring_pattern: null,
      p_is_outsourced: false,
      p_vendors: []
    }
    
    const { data: newSchedule, error: scheduleError } = await supabase
      .rpc('create_schedule_with_vendors', scheduleData)
    
    if (scheduleError) {
      console.log('❌ Error creating schedule:', scheduleError)
      return
    }
    
    console.log(`   ✅ Schedule created: ${newSchedule.custom_id}`)
    console.log(`   📅 Scheduled for: ${newSchedule.scheduled_date}`)
    console.log(`   🆔 Schedule ID: ${newSchedule.id}`)
    
    // Step 4: Check if SharePoint folder was created immediately
    console.log('\n4️⃣ Checking immediate SharePoint folder creation...')
    const { data: scheduleCheck, error: checkError } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', newSchedule.id)
      .single()
    
    if (checkError) {
      console.log('❌ Error checking schedule:', checkError)
      return
    }
    
    if (scheduleCheck.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created immediately!')
      console.log(`   📁 Folder ID: ${scheduleCheck.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${scheduleCheck.sharepoint_folder_url}`)
      console.log(`   🌐 Share Link: ${scheduleCheck.sharepoint_share_link || 'Not set'}`)
    } else {
      console.log('   ⚠️  SharePoint folder not created immediately')
      
      // Step 5: Try to trigger folder creation manually
      console.log('\n5️⃣ Triggering manual SharePoint folder creation...')
      
      try {
        const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          const result = await response.json()
          console.log('   ✅ Manual trigger successful')
          
          // Check again
          const { data: finalCheck } = await supabase
            .from('schedules')
            .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
            .eq('id', newSchedule.id)
            .single()
          
          if (finalCheck && finalCheck.sharepoint_folder_id) {
            console.log('   ✅ SharePoint folder created after manual trigger!')
            console.log(`   📁 Folder ID: ${finalCheck.sharepoint_folder_id}`)
            console.log(`   🔗 Folder URL: ${finalCheck.sharepoint_folder_url}`)
            console.log(`   🌐 Share Link: ${finalCheck.sharepoint_share_link || 'Not set'}`)
          } else {
            console.log('   ❌ SharePoint folder still not created')
          }
        } else {
          console.log('   ❌ Manual trigger failed:', response.status)
        }
      } catch (error) {
        console.log('   ❌ Manual trigger error:', error.message)
      }
    }
    
    console.log('\n🎯 Test Complete!')
    console.log(`\n💡 You can clean up by deleting schedule ${newSchedule.custom_id} if needed.`)
    
  } catch (error) {
    console.error('❌ Test error:', error)
  }
}

testNewScheduleCreation()