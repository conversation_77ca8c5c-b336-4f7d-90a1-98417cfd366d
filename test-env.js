require('dotenv').config({ path: '.env.local' });

console.log('Environment Variables Test:');
console.log('SUPABASE_SERVICE_ROLE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);
console.log('NEXT_PUBLIC_SUPABASE_URL exists:', !!process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log('MICROSOFT_CLIENT_ID exists:', !!process.env.MICROSOFT_CLIENT_ID);

if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Service key starts with:', process.env.SUPABASE_SERVICE_ROLE_KEY.substring(0, 20) + '...');
} else {
  console.log('❌ SUPABASE_SERVICE_ROLE_KEY is not loaded');
}