require('dotenv').config({ path: '.env.local' });

async function testSchedulesApiCreate() {
  try {
    console.log('🧪 Testing schedulesApi.create method...');
    
    // Test the actual API endpoint that uses schedulesApi.create
    const scheduleData = {
      project_id: 'test-project-id',
      pilot_id: 'test-pilot-id',
      scheduled_date: '2025-01-25',
      amount: 5000,
      location: 'Test Location',
      notes: 'Test schedule creation via schedulesApi.create'
    };
    
    console.log('📝 Creating schedule via API that uses schedulesApi.create...');
    
    // Call an API endpoint that uses schedulesApi.create
    const response = await fetch('http://localhost:3002/api/schedules', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(scheduleData)
    });
    
    console.log('📊 Response status:', response.status);
    console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('📊 Response body:', responseText);
    
    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText} - ${responseText}`);
    }
    
    const responseData = JSON.parse(responseText);
    console.log('✅ Schedule creation response:', responseData);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSchedulesApiCreate();