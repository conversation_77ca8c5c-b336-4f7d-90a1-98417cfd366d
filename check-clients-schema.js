const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU'

const supabase = createClient(supabaseUrl, supabaseKey)

async function checkClientsSchema() {
  console.log('🔍 CHECKING CLIENTS TABLE SCHEMA')
  console.log('================================')
  
  // Try to get any existing client to see the structure
  const { data: clients, error: clientsError } = await supabase
    .from('clients')
    .select('*')
    .limit(1)
    
  if (clientsError) {
    console.error('❌ Error fetching clients:', clientsError)
  } else {
    console.log('✅ Clients table accessible')
    if (clients && clients.length > 0) {
      console.log('📋 Client columns:', Object.keys(clients[0]))
      console.log('📄 Sample client:', clients[0])
    } else {
      console.log('📭 No clients found')
    }
  }
  
  // Try to insert a minimal client to see what's required
  console.log('\n🧪 Testing minimal client insert...')
  const { data: insertData, error: insertError } = await supabase
    .from('clients')
    .insert({
      name: 'Test Client',
      email: '<EMAIL>'
    })
    .select()
    
  if (insertError) {
    console.error('❌ Insert error:', insertError)
  } else {
    console.log('✅ Insert successful:', insertData)
  }
}

checkClientsSchema().catch(console.error)