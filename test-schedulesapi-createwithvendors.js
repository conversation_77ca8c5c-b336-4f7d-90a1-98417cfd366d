#!/usr/bin/env node

import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

async function testSchedulesApiCreateWithVendors() {
  console.log('🧪 Testing schedulesApi.createWithVendors method...\n')

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // 1. Initialize background jobs via API
    console.log('🔧 Step 1: Initializing background jobs system...')
    try {
      const initResponse = await fetch('http://localhost:3002/api/init')
      if (initResponse.ok) {
        const initResult = await initResponse.json()
        console.log('   ✅ Background jobs initialized:', initResult.status)
      } else {
        console.log('   ❌ Failed to initialize background jobs:', initResponse.status)
      }
    } catch (initError) {
      console.log('   ❌ Error initializing background jobs:', initError.message)
    }

    // 2. Test schedulesApi.createWithVendors via the new API endpoint
    console.log('\n🎯 Step 2: Testing schedulesApi.createWithVendors...')
    
    try {
      const createResponse = await fetch('http://localhost:3002/api/test-createwithvendors')

      if (createResponse.ok) {
        const createResult = await createResponse.json()
        console.log('   ✅ schedulesApi.createWithVendors response:', createResult.message)
        console.log('   📋 Schedule created:', createResult.schedule?.custom_id)
        
        const scheduleId = createResult.schedule?.id

        // 3. Check background jobs status immediately
        console.log('\n📊 Step 3: Checking background jobs status immediately...')
        try {
          const jobsResponse = await fetch('http://localhost:3002/api/background-jobs')
          if (jobsResponse.ok) {
            const jobsResult = await jobsResponse.json()
            console.log(`   📋 Background jobs: ${jobsResult.jobs?.length || 0} jobs in queue`)
            if (jobsResult.jobs?.length > 0) {
              jobsResult.jobs.forEach((job, index) => {
                console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status} - ${job.entityType}:${job.entityId}`)
              })
            } else {
              console.log('   ⚠️  No background jobs found - this indicates the issue!')
            }
          } else {
            console.log('   ❌ Failed to get background jobs:', jobsResponse.status)
          }
        } catch (jobsError) {
          console.log('   ❌ Error getting background jobs:', jobsError.message)
        }

        // 4. Wait for potential background job processing
        console.log('\n⏳ Step 4: Waiting for potential background job processing...')
        await new Promise(resolve => setTimeout(resolve, 10000)) // Wait 10 seconds

        // 5. Check background jobs status after processing
        console.log('\n📊 Step 5: Checking background jobs status after processing...')
        try {
          const jobsResponse = await fetch('http://localhost:3002/api/background-jobs')
          if (jobsResponse.ok) {
            const jobsResult = await jobsResponse.json()
            console.log(`   📋 Background jobs: ${jobsResult.jobs?.length || 0} jobs in queue`)
            if (jobsResult.jobs?.length > 0) {
              jobsResult.jobs.forEach((job, index) => {
                console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status} - ${job.entityType}:${job.entityId}`)
              })
            }
          } else {
            console.log('   ❌ Failed to get background jobs:', jobsResponse.status)
          }
        } catch (jobsError) {
          console.log('   ❌ Error getting background jobs:', jobsError.message)
        }

        // 6. Check if SharePoint folder was created
        if (scheduleId) {
          console.log('\n🔍 Step 6: Checking SharePoint folder creation...')
          const { data: updatedSchedule, error: fetchError } = await supabase
            .from('schedules')
            .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
            .eq('id', scheduleId)
            .single()

          if (fetchError) {
            console.log(`   ❌ Error fetching updated schedule: ${fetchError.message}`)
          } else {
            console.log(`   📋 Schedule: ${updatedSchedule.custom_id}`)
            console.log(`   📋 SharePoint Folder ID: ${updatedSchedule.sharepoint_folder_id || 'null'}`)
            console.log(`   📋 SharePoint Folder URL: ${updatedSchedule.sharepoint_folder_url || 'null'}`)
            console.log(`   📋 SharePoint Share Link: ${updatedSchedule.sharepoint_share_link || 'null'}`)

            if (updatedSchedule.sharepoint_folder_id) {
              console.log('   ✅ SharePoint folder was created successfully!')
            } else {
              console.log('   ❌ SharePoint folder was NOT created - confirming the issue!')
            }
          }

          // 7. Cleanup test schedule
          console.log('\n🧹 Step 7: Cleaning up test schedule...')
          const { error: deleteError } = await supabase
            .from('schedules')
            .delete()
            .eq('id', scheduleId)

          if (deleteError) {
            console.log(`   ❌ Error deleting test schedule: ${deleteError.message}`)
          } else {
            console.log('   ✅ Test schedule cleaned up successfully')
          }
        }

      } else {
        const errorText = await createResponse.text()
        console.log('   ❌ schedulesApi.createWithVendors failed:', createResponse.status, errorText)
      }
    } catch (apiError) {
      console.log('   ❌ Error calling schedulesApi.createWithVendors:', apiError.message)
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('📋 Full error:', error)
  }
}

testSchedulesApiCreateWithVendors()