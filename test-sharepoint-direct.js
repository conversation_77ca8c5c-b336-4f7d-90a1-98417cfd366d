#!/usr/bin/env node

/**
 * Direct SharePoint folder creation test
 * This script tests the SharePoint folder creation functionality directly
 */

require('dotenv').config({ path: '.env.local' })

async function testSharePointDirect() {
  console.log('🧪 Testing SharePoint folder creation directly...\n')

  try {
    // 1. Check environment variables
    console.log('1️⃣ Checking Microsoft Graph API credentials...')
    const requiredEnvs = ['MICROSOFT_CLIENT_ID', 'MICROSOFT_CLIENT_SECRET', 'MICROSOFT_TENANT_ID']
    const missingEnvs = requiredEnvs.filter(env => !process.env[env])
    
    if (missingEnvs.length > 0) {
      console.log('❌ Missing environment variables:', missingEnvs.join(', '))
      return
    }
    console.log('✅ All Microsoft Graph API credentials are set')

    // 2. Test API endpoint directly
    console.log('\n2️⃣ Testing SharePoint folder creation via API...')
    
    // Get a recent schedule to test with
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    const { data: schedules, error } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id')
      .order('created_at', { ascending: false })
      .limit(1)

    if (error || !schedules || schedules.length === 0) {
      console.log('❌ No schedules found to test with')
      return
    }

    const schedule = schedules[0]
    console.log(`   Using schedule: ${schedule.custom_id} (${schedule.id})`)
    
    if (schedule.sharepoint_folder_id) {
      console.log('   ⚠️  Schedule already has SharePoint folder, testing anyway...')
    }

    // 3. Test SharePoint folder creation via API
    console.log('\n3️⃣ Testing SharePoint folder creation via API...')
    try {
      const response = await fetch(`http://localhost:3000/api/ensure-sharepoint-folder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scheduleId: schedule.id
        })
      })

      const result = await response.json()
      
      if (response.ok) {
        console.log('   ✅ SharePoint folder creation API call successful')
        console.log('   📁 Result:', JSON.stringify(result, null, 2))
      } else {
        console.log('   ❌ SharePoint folder creation API call failed')
        console.log('   📁 Error:', JSON.stringify(result, null, 2))
      }
      
      // 4. Verify in database
      console.log('\n4️⃣ Verifying folder creation in database...')
      const { data: updatedSchedule } = await supabase
        .from('schedules')
        .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
        .eq('id', schedule.id)
        .single()

      if (updatedSchedule?.sharepoint_folder_id) {
        console.log('   ✅ SharePoint folder successfully created and stored in database')
        console.log('   📁 Folder ID:', updatedSchedule.sharepoint_folder_id)
        console.log('   🔗 Folder URL:', updatedSchedule.sharepoint_folder_url)
        console.log('   📤 Share Link:', updatedSchedule.sharepoint_share_link)
      } else {
        console.log('   ❌ SharePoint folder not found in database')
      }

    } catch (sharepointError) {
      console.log('❌ SharePoint folder creation failed:', sharepointError.message)
      console.log('   Full error:', sharepointError)
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('   Full error:', error)
  }
}

// Run the test
testSharePointDirect().catch(console.error)