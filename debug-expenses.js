const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

const supabase = createClient(supabaseUrl, supabaseKey)

async function debugExpenses() {
  console.log('🔍 Debugging expenses for vendor payments...')
  
  // 1. Check all expenses with amount 4000
  const { data: expenses4000, error: expensesError } = await supabase
    .from('expenses')
    .select(`
      id,
      amount,
      description,
      category,
      project_id,
      vendor_id,
      created_at,
      project:projects(name, vendor_payment_status, vendor_payment_amount),
      vendor:outsourcing_vendors(name)
    `)
    .eq('amount', 4000)
    .order('created_at', { ascending: false })
    
  if (expensesError) {
    console.error('❌ Error fetching expenses:', expensesError)
    return
  }
  
  console.log(`💰 Found ${expenses4000.length} expenses with amount ₹4,000:`)
  expenses4000.forEach(expense => {
    console.log(`  - ID: ${expense.id}`)
    console.log(`    Description: ${expense.description}`)
    console.log(`    Category: ${expense.category}`)
    console.log(`    Project: ${expense.project?.name || 'No project'}`)
    console.log(`    Vendor: ${expense.vendor?.name || 'No vendor'}`)
    console.log(`    Project Payment Status: ${expense.project?.vendor_payment_status || 'None'}`)
    console.log(`    Project Payment Amount: ₹${expense.project?.vendor_payment_amount || 0}`)
    console.log(`    Created: ${expense.created_at}`)
    console.log('    ---')
  })
  
  // 2. Check all outsourcing expenses
  const { data: outsourcingExpenses, error: outsourcingError } = await supabase
    .from('expenses')
    .select(`
      id,
      amount,
      description,
      category,
      project_id,
      vendor_id,
      created_at,
      project:projects(name, vendor_payment_status, vendor_payment_amount),
      vendor:outsourcing_vendors(name)
    `)
    .eq('category', 'outsourcing')
    .order('created_at', { ascending: false })
    
  if (outsourcingError) {
    console.error('❌ Error fetching outsourcing expenses:', outsourcingError)
    return
  }
  
  console.log(`\n📋 Found ${outsourcingExpenses.length} outsourcing expenses:`)
  outsourcingExpenses.forEach(expense => {
    console.log(`  - ₹${expense.amount} - ${expense.description} (${expense.project?.name}) - Vendor: ${expense.vendor?.name || 'No vendor'}`)
  })
  
  // 3. Check all projects with vendor payment amounts
  const { data: projectsWithPayments, error: projectsError } = await supabase
    .from('projects')
    .select('id, name, vendor_payment_status, vendor_payment_amount')
    .not('vendor_payment_amount', 'is', null)
    .gt('vendor_payment_amount', 0)
    
  if (projectsError) {
    console.error('❌ Error fetching projects with payments:', projectsError)
    return
  }
  
  console.log(`\n🏗️ Found ${projectsWithPayments.length} projects with vendor payments:`)
  projectsWithPayments.forEach(project => {
    console.log(`  - ${project.name}: ₹${project.vendor_payment_amount} (${project.vendor_payment_status})`)
  })
}

debugExpenses().catch(console.error)