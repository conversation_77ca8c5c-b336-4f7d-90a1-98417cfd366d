import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY // Use service role key to bypass RLS

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testScheduleCreationWithSharePoint() {
  console.log('🚀 Testing schedule creation with SharePoint folder functionality...\n')

  try {
    // 1. Get existing project and pilot data
    console.log('📋 Step 1: Getting existing data...')
    
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1)

    if (projectsError || !projects || projects.length === 0) {
      throw new Error('No projects found for testing')
    }

    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1)

    if (usersError || !users || users.length === 0) {
      throw new Error('No users found for testing')
    }

    const project = projects[0]
    const user = users[0]

    console.log(`   ✅ Using project: ${project.name} (${project.id})`)
    console.log(`   ✅ Using user: ${user.name} (${user.role}) (${user.id})`)

    // 2. Create a new schedule using the create_schedule_with_vendors RPC
    console.log('\n📅 Step 2: Creating new schedule...')
    
    const scheduleData = {
      p_project_id: project.id,
      p_scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      p_scheduled_end_date: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString(), // 8 days from now
      p_pilot_id: user.id,
      p_amount: 25000,
      p_location: 'Test Location for SharePoint Testing',
      p_google_maps_link: 'https://maps.google.com/test',
      p_notes: 'Test schedule for SharePoint folder creation debugging',
      p_is_recurring: false,
      p_recurring_pattern: null,
      p_is_outsourced: false,
      p_vendors: [] // No vendors for this test
    }

    const { data: newSchedule, error: scheduleError } = await supabase
      .rpc('create_schedule_with_vendors', scheduleData)

    if (scheduleError) {
      throw new Error(`Failed to create schedule: ${scheduleError.message}`)
    }

    console.log(`   ✅ Schedule created: ${newSchedule.custom_id} (${newSchedule.id})`)

    // 3. Wait a moment for any async operations
    console.log('\n⏳ Step 3: Waiting for SharePoint folder creation...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // 4. Check if SharePoint folder was created automatically
    console.log('\n🔍 Step 4: Checking SharePoint folder status...')
    
    const { data: updatedSchedule, error: checkError } = await supabase
      .from('schedules')
      .select('*')
      .eq('id', newSchedule.id)
      .single()

    if (checkError) {
      throw new Error(`Failed to check schedule: ${checkError.message}`)
    }

    if (updatedSchedule.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder was created automatically!')
      console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
      console.log(`   📤 Share Link: ${updatedSchedule.sharepoint_share_link}`)
    } else {
      console.log('   ❌ SharePoint folder was NOT created automatically')
      
      // 5. Try manual trigger via API
      console.log('\n🔧 Step 5: Attempting manual SharePoint folder creation...')
      
      const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scheduleIds: [newSchedule.id]
        })
      })

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log('   📊 API Response:', JSON.stringify(result, null, 2))

      // Check again after manual trigger
      const { data: finalSchedule, error: finalError } = await supabase
        .from('schedules')
        .select('*')
        .eq('id', newSchedule.id)
        .single()

      if (finalError) {
        throw new Error(`Failed to check final schedule: ${finalError.message}`)
      }

      if (finalSchedule.sharepoint_folder_id) {
        console.log('   ✅ SharePoint folder created via manual trigger!')
        console.log(`   📁 Folder ID: ${finalSchedule.sharepoint_folder_id}`)
        console.log(`   🔗 Folder URL: ${finalSchedule.sharepoint_folder_url}`)
        console.log(`   📤 Share Link: ${finalSchedule.sharepoint_share_link}`)
      } else {
        console.log('   ❌ SharePoint folder creation failed even with manual trigger')
      }
    }

    console.log('\n🎯 Test complete!')
    console.log('\n📋 Summary:')
    console.log(`   Schedule ID: ${newSchedule.id}`)
    console.log(`   Schedule Custom ID: ${newSchedule.custom_id}`)
    console.log(`   SharePoint Status: ${updatedSchedule.sharepoint_folder_id ? 'Created' : 'Not Created'}`)

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('Stack trace:', error.stack)
    process.exit(1)
  }
}

testScheduleCreationWithSharePoint()