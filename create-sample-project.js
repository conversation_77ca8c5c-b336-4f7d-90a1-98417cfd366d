const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'

console.log('🔗 Connecting to Supabase...')
const supabase = createClient(supabaseUrl, supabaseKey)

async function createSampleData() {
  console.log('🚀 Creating sample data for vendor payment testing...')
  
  // Step 1: Create a client
  console.log('\n👥 Creating sample client...')
  const { data: client, error: clientError } = await supabase
    .from('clients')
    .insert({
      name: 'Sample Client',
      email: '<EMAIL>',
      phone: '+91 9876543210'
    })
    .select()
    .single()
    
  if (clientError) {
    console.error('❌ Error creating client:', clientError)
    return
  }
  
  console.log('✅ Client created:', client.name, `(ID: ${client.id})`)
  
  // Step 2: Create a project
  console.log('\n🏗️ Creating sample project...')
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .insert({
      name: 'Sample Wedding Photography',
      client_id: client.id,
      status: 'active',
      total_amount: 50000,
      amount_received: 10000,
      amount_pending: 40000,
      vendor_payment_status: 'pending',
      vendor_payment_amount: 4000
    })
    .select()
    .single()
    
  if (projectError) {
    console.error('❌ Error creating project:', projectError)
    return
  }
  
  console.log('✅ Project created:', project.name, `(ID: ${project.id})`)
  
  // Step 3: Create an expense for Arun photography
  console.log('\n💰 Creating expense for Arun photography...')
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  const { data: expense, error: expenseError } = await supabase
    .from('expenses')
    .insert({
      project_id: project.id,
      category: 'outsourcing',
      description: 'Photography services by Arun photography',
      amount: 4000,
      date: new Date().toISOString().split('T')[0], // Today's date
      vendor_id: vendorId,
      user_id: 'b5ebc4a2-d527-4773-8ed7-996cae77ec17' // <EMAIL>
    })
    .select()
    .single()
    
  if (expenseError) {
    console.error('❌ Error creating expense:', expenseError)
    return
  }
  
  console.log('✅ Expense created:', `₹${expense.amount}`, `(ID: ${expense.id})`)
  
  // Step 4: Create a schedule and link it to the vendor
  console.log('\n📅 Creating schedule and linking to vendor...')
  const { data: schedule, error: scheduleError } = await supabase
    .from('schedules')
    .insert({
      project_id: project.id,
      scheduled_date: new Date().toISOString(),
      scheduled_end_date: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(), // 4 hours later
      location: 'Wedding Venue',
      amount: 4000,
      notes: 'Photography session for the wedding project',
      status: 'scheduled'
    })
    .select()
    .single()
    
  if (scheduleError) {
    console.error('❌ Error creating schedule:', scheduleError)
    return
  }
  
  console.log('✅ Schedule created:', schedule.title, `(ID: ${schedule.id})`)
  
  // Step 5: Link the vendor to the schedule
  console.log('\n🔗 Linking vendor to schedule...')
  const { data: scheduleVendor, error: scheduleVendorError } = await supabase
    .from('schedule_vendors')
    .insert({
      schedule_id: schedule.id,
      vendor_id: vendorId
    })
    .select()
    .single()
    
  if (scheduleVendorError) {
    console.error('❌ Error linking vendor to schedule:', scheduleVendorError)
    return
  }
  
  console.log('✅ Vendor linked to schedule successfully!')
  
  console.log('\n🎉 Sample data created successfully!')
  console.log('\n📊 Summary:')
  console.log(`- Client: ${client.name}`)
  console.log(`- Project: ${project.name}`)
  console.log(`- Schedule: ${schedule.title} (Outsourced: ₹${schedule.outsourcing_cost})`)
  console.log(`- Expense: ₹${expense.amount} for ${expense.description}`)
  console.log(`- Vendor: Arun photography linked to schedule`)
  
  console.log('\n📝 Next steps:')
  console.log('1. Refresh the vendor details page in your browser')
  console.log('2. Clear browser cache if needed')
  console.log('3. The vendor should now show ₹4,000 pending payment')
  console.log('4. You can test the payment workflow by marking it as paid')
  
  return {
    client,
    project,
    schedule,
    expense,
    scheduleVendor
  }
}

createSampleData().catch(console.error)