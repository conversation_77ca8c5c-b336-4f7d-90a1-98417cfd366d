#!/usr/bin/env node

/**
 * Test script to simulate the exact API call that the UI makes when creating a schedule
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testUIAPICall() {
  console.log('🧪 Testing UI API Call for Schedule Creation')
  console.log('=' .repeat(50))

  try {
    // Step 1: Get test data
    console.log('\n1️⃣ Getting test data...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select(`
        id, 
        custom_id, 
        name,
        client_id,
        clients!inner (
          id,
          custom_id,
          name
        )
      `)
      .limit(1)

    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)

    if (!projects?.length || !users?.length) {
      console.log('❌ No projects or users found')
      return
    }

    const project = projects[0]
    const user = users[0]
    
    console.log(`   📋 Using project: ${project.custom_id} ${project.name}`)
    console.log(`   🏢 Client: ${project.clients.custom_id} ${project.clients.name}`)

    // Step 2: Make the exact API call that the UI makes
    console.log('\n2️⃣ Making API call to create schedule...')
    
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      pilot_id: user.id,
      amount: 2500,
      location: 'Test Location via UI API Call',
      google_maps_link: 'https://maps.google.com',
      notes: 'Test schedule created via UI API call simulation',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }

    const vendors = []

    // This is the exact call that the UI makes
    const response = await fetch('http://localhost:3000/api/schedules', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        schedule: scheduleData,
        vendors: vendors
      })
    })

    if (!response.ok) {
      console.log(`   ❌ API call failed: ${response.status} ${response.statusText}`)
      const errorText = await response.text()
      console.log(`   🔍 Error response: ${errorText}`)
      return
    }

    const result = await response.json()
    console.log(`   ✅ Schedule created via API: ${result.custom_id} (ID: ${result.id})`)

    // Step 3: Check SharePoint folder creation immediately
    console.log('\n3️⃣ Checking immediate SharePoint folder creation...')
    
    const { data: immediateCheck } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', result.id)
      .single()

    if (immediateCheck?.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created immediately!')
      console.log(`   📁 Folder ID: ${immediateCheck.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${immediateCheck.sharepoint_folder_url}`)
      console.log(`   📤 Share Link: ${immediateCheck.sharepoint_share_link}`)
    } else {
      console.log('   ⏳ SharePoint folder not created immediately')
      
      // Step 4: Wait and check again
      console.log('\n4️⃣ Waiting 10 seconds for background processing...')
      await new Promise(resolve => setTimeout(resolve, 10000))
      
      const { data: delayedCheck } = await supabase
        .from('schedules')
        .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
        .eq('id', result.id)
        .single()

      if (delayedCheck?.sharepoint_folder_id) {
        console.log('   ✅ SharePoint folder created after delay!')
        console.log(`   📁 Folder ID: ${delayedCheck.sharepoint_folder_id}`)
        console.log(`   🔗 Folder URL: ${delayedCheck.sharepoint_folder_url}`)
        console.log(`   📤 Share Link: ${delayedCheck.sharepoint_share_link}`)
      } else {
        console.log('   ❌ SharePoint folder still not created after delay')
        
        // Step 5: Check background jobs
        console.log('\n5️⃣ Checking background job status...')
        try {
          const jobResponse = await fetch('http://localhost:3000/api/background-jobs')
          const jobData = await jobResponse.json()
          
          if (jobData.success && jobData.jobs) {
            const scheduleJobs = jobData.jobs.filter(job => 
              job.entityType === 'schedule' && 
              job.entityId === result.id
            )
            
            if (scheduleJobs.length > 0) {
              console.log(`   📋 Found ${scheduleJobs.length} background job(s) for this schedule:`)
              scheduleJobs.forEach(job => {
                console.log(`      - Job ${job.id}: ${job.status} (attempts: ${job.attempts}/${job.maxAttempts})`)
                if (job.error) {
                  console.log(`        Error: ${job.error}`)
                }
              })
            } else {
              console.log('   📋 No background jobs found for this schedule')
            }
          }
        } catch (jobError) {
          console.log('   ❌ Error checking background jobs:', jobError.message)
        }

        // Step 6: Try manual trigger
        console.log('\n6️⃣ Attempting manual SharePoint folder creation...')
        
        try {
          const manualResponse = await fetch('http://localhost:3000/api/test-sharepoint', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              scheduleId: result.id
            })
          })

          const manualResult = await manualResponse.json()
          
          if (manualResult.success) {
            console.log('   ✅ Manual SharePoint folder creation succeeded')
            
            // Check database again
            const { data: manualCheck } = await supabase
              .from('schedules')
              .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
              .eq('id', result.id)
              .single()

            if (manualCheck?.sharepoint_folder_id) {
              console.log(`   📁 Folder ID: ${manualCheck.sharepoint_folder_id}`)
              console.log(`   🔗 Folder URL: ${manualCheck.sharepoint_folder_url}`)
              console.log(`   📤 Share Link: ${manualCheck.sharepoint_share_link}`)
            }
          } else {
            console.log('   ❌ Manual SharePoint folder creation failed:', manualResult.message)
          }
        } catch (manualError) {
          console.log('   ❌ Error during manual trigger:', manualError.message)
        }
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testUIAPICall()
