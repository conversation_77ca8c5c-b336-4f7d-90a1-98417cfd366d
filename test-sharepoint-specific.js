// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

async function testSharePointForSpecificSchedule() {
  try {
    console.log('🧪 Testing SharePoint folder creation for specific schedule...')
    
    // Import the SharePointService
    const { SharePointService } = await import('./src/lib/sharepoint-service.ts')
    
    // Test with the most recent schedule
    const scheduleId = 'CYM-25-043' // From our debug output
    
    console.log(`🎯 Testing SharePoint folder creation for schedule: ${scheduleId}`)
    
    // Find the actual UUID for this schedule
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    const { data: schedule, error } = await supabase
      .from('schedules')
      .select('id, custom_id')
      .eq('custom_id', scheduleId)
      .single()
    
    if (error || !schedule) {
      console.error('❌ Schedule not found:', error)
      return
    }
    
    console.log(`📋 Found schedule UUID: ${schedule.id}`)
    
    // Test SharePoint folder creation
    const result = await SharePointService.ensureScheduleFolder(schedule.id)
    
    if (result) {
      console.log('✅ SharePoint folder creation successful!')
    } else {
      console.log('❌ SharePoint folder creation failed')
    }
    
  } catch (error) {
    console.error('❌ Test error:', error)
    console.error('Stack trace:', error.stack)
  }
}

testSharePointForSpecificSchedule()