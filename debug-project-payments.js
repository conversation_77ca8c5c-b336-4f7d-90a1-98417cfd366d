const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU'

console.log('🔗 Connecting to Supabase...')
const supabase = createClient(supabaseUrl, supabaseKey)

async function debugProjectPayments() {
  console.log('🔍 Debugging project vendor payments...')
  
  // 1. Check all projects with vendor payment amounts
  const { data: projects, error: projectsError } = await supabase
    .from('projects')
    .select(`
      id,
      name,
      vendor_payment_status,
      vendor_payment_amount,
      vendor_payment_due_date,
      vendor_payment_date,
      vendor_payment_notes,
      created_at
    `)
    .not('vendor_payment_amount', 'is', null)
    .order('created_at', { ascending: false })
    
  if (projectsError) {
    console.error('❌ Error fetching projects:', projectsError)
    return
  }
  
  console.log(`📋 Found ${projects.length} projects with vendor payment amounts:`)
  
  for (const project of projects) {
    console.log(`\n🏗️ Project: ${project.name}`)
    console.log(`  💰 Amount: ₹${project.vendor_payment_amount}`)
    console.log(`  📊 Status: ${project.vendor_payment_status}`)
    console.log(`  📅 Due Date: ${project.vendor_payment_due_date || 'Not set'}`)
    console.log(`  ✅ Payment Date: ${project.vendor_payment_date || 'Not paid'}`)
    console.log(`  📝 Notes: ${project.vendor_payment_notes || 'None'}`)
    
    // Check schedules for this project
    const { data: schedules, error: schedulesError } = await supabase
      .from('schedules')
      .select(`
        id,
        scheduled_date,
        is_outsourced,
        vendors:schedule_vendors(
          vendor_id,
          cost,
          vendor:outsourcing_vendors(name)
        )
      `)
      .eq('project_id', project.id)
      
    if (!schedulesError && schedules) {
      console.log(`  📅 Schedules: ${schedules.length}`)
      schedules.forEach(schedule => {
        console.log(`    - ${schedule.scheduled_date} (Outsourced: ${schedule.is_outsourced})`)
        if (schedule.vendors && schedule.vendors.length > 0) {
          schedule.vendors.forEach(sv => {
            console.log(`      Vendor: ${sv.vendor?.name} - ₹${sv.cost}`)
          })
        }
      })
    }
  }
  
  // 2. Check all projects with any vendor payment status
  const { data: allProjects, error: allProjectsError } = await supabase
    .from('projects')
    .select(`
      id,
      name,
      vendor_payment_status,
      vendor_payment_amount
    `)
    .not('vendor_payment_status', 'is', null)
    .order('name')
    
  if (!allProjectsError && allProjects) {
    console.log(`\n📊 All projects with vendor payment status (${allProjects.length}):`)
    allProjects.forEach(project => {
      console.log(`  - ${project.name}: ${project.vendor_payment_status} (₹${project.vendor_payment_amount || 0})`)
    })
  }
  
  // 3. Check if there are any projects with ₹7,000 amount
  const { data: sevenKProjects, error: sevenKError } = await supabase
    .from('projects')
    .select('*')
    .eq('vendor_payment_amount', 7000)
    
  if (!sevenKError && sevenKProjects) {
    console.log(`\n💰 Projects with ₹7,000 vendor payment amount (${sevenKProjects.length}):`)
    sevenKProjects.forEach(project => {
      console.log(`  - ${project.name}: ${project.vendor_payment_status}`)
    })
  }
}

debugProjectPayments().catch(console.error)