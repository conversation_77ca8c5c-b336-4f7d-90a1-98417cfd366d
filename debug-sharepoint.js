// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://zn6bn.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment')
  console.log('Available env vars:', Object.keys(process.env).filter(k => k.includes('SUPABASE')))
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function debugSharePointIssue() {
  try {
    console.log('🔍 Debugging SharePoint folder creation issue...')
    
    // Get the most recent schedule
    const { data: schedules, error } = await supabase
      .from('schedules')
      .select(`
        id, custom_id, scheduled_date,
        project:projects(
          id, custom_id, name,
          client:clients(id, custom_id, name)
        )
      `)
      .order('created_at', { ascending: false })
      .limit(5)

    if (error) {
      console.error('❌ Error fetching schedules:', error)
      return
    }

    console.log('📋 Recent schedules:')
    schedules.forEach((schedule, index) => {
      const project = Array.isArray(schedule.project) ? schedule.project[0] : schedule.project
      const client = project && Array.isArray(project.client) ? project.client[0] : project?.client
      
      console.log(`${index + 1}. Schedule: ${schedule.custom_id}`)
      console.log(`   Project: ${project?.custom_id} - ${project?.name}`)
      console.log(`   Client: ${client?.custom_id} - ${client?.name}`)
      console.log(`   Date: ${schedule.scheduled_date}`)
      console.log(`   Has all required data: ${!!(schedule.custom_id && project?.custom_id && project?.name && client?.custom_id && client?.name)}`)
      console.log('')
    })

    // Check Microsoft Graph credentials
    console.log('🔑 Checking Microsoft Graph credentials...')
    const clientId = process.env.MICROSOFT_CLIENT_ID
    const clientSecret = process.env.MICROSOFT_CLIENT_SECRET
    const tenantId = process.env.MICROSOFT_TENANT_ID

    console.log(`MICROSOFT_CLIENT_ID: ${clientId ? '✅ Set' : '❌ Missing'}`)
    console.log(`MICROSOFT_CLIENT_SECRET: ${clientSecret ? '✅ Set' : '❌ Missing'}`)
    console.log(`MICROSOFT_TENANT_ID: ${tenantId ? '✅ Set' : '❌ Missing'}`)

    if (clientId && clientSecret && tenantId) {
      console.log('🧪 Testing Microsoft Graph authentication...')
      try {
        const response = await fetch(
          `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
              client_id: clientId,
              scope: 'https://graph.microsoft.com/.default',
              client_secret: clientSecret,
              grant_type: 'client_credentials'
            })
          }
        )

        if (response.ok) {
          console.log('✅ Microsoft Graph authentication successful')
        } else {
          const errorText = await response.text()
          console.log('❌ Microsoft Graph authentication failed:', response.status, errorText)
        }
      } catch (authError) {
        console.log('❌ Microsoft Graph authentication error:', authError.message)
      }
    }

  // Check background job queue
    console.log('🔄 Checking background job queue...');
    try {
      // We need to dynamically import the queue to get the current instance
      const { getAllJobs } = require('./src/lib/background-jobs.ts');
      const jobs = getAllJobs();
      
      if (jobs.length > 0) {
        console.log(`   📊 Found ${jobs.length} jobs in the queue:`);
        jobs.forEach(job => {
          console.log(`     - Job ID: ${job.id}`);
          console.log(`       Status: ${job.status}`);
          console.log(`       Attempts: ${job.attempts}`);
          console.log(`       Error: ${job.error || 'None'}`);
        });
      } else {
        console.log('   ✅ Background job queue is empty');
      }
    } catch (queueError) {
      console.log('   ❌ Error getting background jobs:', queueError.message);
    }

  } catch (error) {
    console.error('❌ Debug error:', error);
  }
}

debugSharePointIssue();