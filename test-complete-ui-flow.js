require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testCompleteUIFlow() {
  console.log('🧪 Testing complete UI flow for SharePoint folder creation...');
  
  try {
    // Step 1: Get a valid project ID
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1);
    
    if (projectError || !projects || projects.length === 0) {
      console.error('❌ Could not get a valid project ID:', projectError);
      return;
    }
    
    const project = projects[0];
    console.log('📋 Using project:', { id: project.id, custom_id: project.custom_id, name: project.name });
    
    // Step 2: Simulate the exact UI flow by calling schedulesApi.createWithVendors
    console.log('\n🎯 Simulating UI flow: calling schedulesApi.createWithVendors...');
    
    // Import the API function (simulating what the UI does)
    const scheduleData = {
      project_id: project.id,
      scheduled_date: '2025-01-20T10:00:00Z',
      scheduled_end_date: '2025-01-20T18:00:00Z',
      pilot_id: null,
      amount: 2500,
      location: 'Test Location for SharePoint',
      google_maps_link: null,
      notes: 'Testing SharePoint folder creation via UI flow',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    };
    
    const vendors = []; // No vendors for this test
    
    // Call the PostgreSQL function directly (this is what schedulesApi.createWithVendors does)
    const { data: newSchedule, error: createError } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: scheduleData.project_id,
      p_scheduled_date: scheduleData.scheduled_date,
      p_scheduled_end_date: scheduleData.scheduled_end_date,
      p_pilot_id: scheduleData.pilot_id,
      p_amount: scheduleData.amount,
      p_location: scheduleData.location,
      p_google_maps_link: scheduleData.google_maps_link,
      p_notes: scheduleData.notes,
      p_is_recurring: scheduleData.is_recurring,
      p_recurring_pattern: scheduleData.recurring_pattern,
      p_is_outsourced: scheduleData.is_outsourced,
      p_vendors: vendors
    });
    
    if (createError) {
      console.error('❌ Error creating schedule:', createError);
      return;
    }
    
    console.log('✅ Schedule created successfully!');
    console.log('📋 Schedule details:', {
      id: newSchedule.id,
      custom_id: newSchedule.custom_id,
      location: newSchedule.location,
      notes: newSchedule.notes
    });
    
    // Step 3: Simulate the background job queuing (this is what happens in createWithVendors)
    console.log('\n🔄 Simulating background job queuing...');
    
    // This simulates the background job queuing that happens in createWithVendors
    try {
      // Make a request to the background job API endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/background-jobs/sharepoint-folder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entityType: 'schedule',
          entityId: newSchedule.id,
          metadata: {
            scheduleId: newSchedule.id,
            customId: newSchedule.custom_id,
            scheduledDate: scheduleData.scheduled_date
          }
        })
      });
      
      if (response.ok) {
        const jobResult = await response.json();
        console.log('✅ Background job queued successfully:', jobResult);
      } else {
        console.log('⚠️ Background job API not available (expected in test environment)');
        console.log('   This would normally queue the SharePoint folder creation job');
      }
    } catch (fetchError) {
      console.log('⚠️ Background job API not reachable (expected in test environment)');
      console.log('   This would normally queue the SharePoint folder creation job');
    }
    
    // Step 4: Wait a moment and check if SharePoint fields are populated
    console.log('\n⏳ Waiting 5 seconds for background job processing...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Step 5: Check the schedule for SharePoint folder details
    console.log('\n🔍 Checking schedule for SharePoint folder details...');
    
    const { data: updatedSchedule, error: fetchError } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', newSchedule.id)
      .single();
    
    if (fetchError) {
      console.error('❌ Error fetching updated schedule:', fetchError);
    } else {
      console.log('📋 Updated schedule SharePoint details:', {
        id: updatedSchedule.id,
        custom_id: updatedSchedule.custom_id,
        sharepoint_folder_id: updatedSchedule.sharepoint_folder_id,
        sharepoint_folder_url: updatedSchedule.sharepoint_folder_url,
        sharepoint_share_link: updatedSchedule.sharepoint_share_link
      });
      
      if (updatedSchedule.sharepoint_folder_id) {
        console.log('✅ SharePoint folder creation appears to be working!');
      } else {
        console.log('⚠️ SharePoint folder not yet created (may still be processing)');
      }
    }
    
    // Step 6: Clean up - delete the test schedule
    console.log('\n🧹 Cleaning up test schedule...');
    const { error: deleteError } = await supabase
      .from('schedules')
      .delete()
      .eq('id', newSchedule.id);
    
    if (deleteError) {
      console.error('❌ Error deleting test schedule:', deleteError);
    } else {
      console.log('✅ Test schedule cleaned up successfully');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testCompleteUIFlow();