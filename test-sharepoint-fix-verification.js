import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testSharePointFix() {
  console.log('🧪 Testing SharePoint Folder Creation Fix')
  console.log('=========================================')
  console.log(`📋 SHAREPOINT_FOLDER_CREATION_MODE: ${process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'undefined (defaults to background-with-fallback)'}`)
  console.log('')

  try {
    // Step 1: Get a project to use
    console.log('📋 Step 1: Getting a project...')
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, client:clients(name)')
      .limit(1)

    if (projectsError) throw projectsError
    if (!projects || projects.length === 0) {
      console.log('❌ No projects found')
      return
    }

    const project = projects[0]
    console.log(`   ✅ Using project: ${project.name} (${project.client?.name})`)

    // Step 2: Create a schedule using the API endpoint (simulating UI behavior)
    console.log('\n🔄 Step 2: Creating schedule via API endpoint...')
    
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      scheduled_end_date: null,
      pilot_id: null,
      amount: 1500,
      location: 'Test Location - SharePoint Fix Verification',
      google_maps_link: '',
      notes: 'Testing SharePoint folder creation fix with sync mode',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false,
      vendors: []
    }

    console.log('   📝 Creating schedule with data:', {
      project_id: scheduleData.project_id,
      scheduled_date: scheduleData.scheduled_date,
      amount: scheduleData.amount,
      location: scheduleData.location
    })

    // Call the API endpoint directly (this is what the UI does)
    const response = await fetch('http://localhost:3001/api/schedules/create-with-vendors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(scheduleData)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`API call failed: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    console.log(`   ✅ Schedule created: ${result.custom_id} (ID: ${result.id})`)

    // Step 3: Check SharePoint folder status immediately
    console.log('\n📁 Step 3: Checking SharePoint folder status...')
    console.log('   📋 SharePoint status:', {
      folder_id: result.sharepoint_folder_id || 'null',
      folder_url: result.sharepoint_folder_url || 'null',
      share_link: result.sharepoint_share_link || 'null'
    })

    // Step 4: Verify in database
    console.log('\n🔍 Step 4: Verifying in database...')
    const { data: dbSchedule, error: dbError } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', result.id)
      .single()

    if (dbError) throw dbError

    console.log('   📋 Database SharePoint status:', {
      folder_id: dbSchedule.sharepoint_folder_id || 'null',
      folder_url: dbSchedule.sharepoint_folder_url || 'null',
      share_link: dbSchedule.sharepoint_share_link || 'null'
    })

    // Step 5: Check background jobs (should be empty with sync mode)
    console.log('\n🔧 Step 5: Checking background jobs...')
    try {
      const jobsResponse = await fetch('http://localhost:3001/api/background-jobs')
      if (jobsResponse.ok) {
        const jobsResult = await jobsResponse.json()
        console.log(`   📋 Background jobs: ${jobsResult.jobs?.length || 0} jobs in queue`)
        if (jobsResult.jobs && jobsResult.jobs.length > 0) {
          jobsResult.jobs.forEach((job, index) => {
            console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status} - ${job.entityType}:${job.entityId}`)
          })
        }
      } else {
        console.log('   ❌ Failed to get background jobs:', jobsResponse.status)
      }
    } catch (jobsError) {
      console.log('   ❌ Error getting background jobs:', jobsError.message)
    }

    // Step 6: Final assessment
    console.log('\n📊 Step 6: Final Assessment')
    console.log('============================')
    
    if (dbSchedule.sharepoint_folder_id && dbSchedule.sharepoint_folder_url) {
      console.log('🎉 SUCCESS: SharePoint folder was created synchronously!')
      console.log(`   📁 Folder ID: ${dbSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${dbSchedule.sharepoint_folder_url}`)
      if (dbSchedule.sharepoint_share_link) {
        console.log(`   📤 Share Link: ${dbSchedule.sharepoint_share_link}`)
      }
      console.log('\n✅ The UI SharePoint folder creation issue has been FIXED!')
    } else {
      console.log('❌ ISSUE PERSISTS: SharePoint folder was NOT created')
      console.log('   This indicates the fix did not work as expected.')
    }

    // Step 7: Cleanup
    console.log('\n🧹 Step 7: Cleaning up test schedule...')
    const { error: deleteError } = await supabase
      .from('schedules')
      .delete()
      .eq('id', result.id)

    if (deleteError) {
      console.log('   ❌ Error deleting test schedule:', deleteError)
    } else {
      console.log('   ✅ Test schedule cleaned up successfully')
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
    console.error('Stack trace:', error.stack)
  }
}

testSharePointFix()