import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)

async function checkSchedules() {
  console.log('🔍 Checking recent schedules...\n')
  
  const { data: schedules, error } = await supabase
    .from('schedules')
    .select('id, custom_id, created_at, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
    .order('created_at', { ascending: false })
    .limit(5)
  
  if (error) {
    console.error('❌ Error:', error)
    return
  }
  
  if (!schedules || schedules.length === 0) {
    console.log('❌ No schedules found in database')
    return
  }
  
  console.log(`📋 Found ${schedules.length} recent schedules:\n`)
  
  schedules.forEach((s, index) => {
    const hasFolder = !!s.sharepoint_folder_id
    const hasUrl = !!s.sharepoint_folder_url
    const hasShareLink = !!s.sharepoint_share_link
    
    console.log(`${index + 1}. ${s.custom_id}`)
    console.log(`   📅 Created: ${s.created_at}`)
    console.log(`   📁 Folder ID: ${hasFolder ? '✅ ' + s.sharepoint_folder_id : '❌ Missing'}`)
    console.log(`   🌐 Folder URL: ${hasUrl ? '✅ ' + s.sharepoint_folder_url : '❌ Missing'}`)
    console.log(`   🔗 Share Link: ${hasShareLink ? '✅ Present' : '❌ Missing'}`)
    console.log(`   📊 Status: ${hasFolder && hasUrl ? '✅ Complete' : '❌ Incomplete'}`)
    console.log('')
  })
  
  // Check if any schedules are missing folders
  const incompleteSchedules = schedules.filter(s => !s.sharepoint_folder_id)
  
  if (incompleteSchedules.length > 0) {
    console.log(`⚠️  ${incompleteSchedules.length} schedules are missing SharePoint folders`)
    console.log('   This could indicate:')
    console.log('   - Background job failed to process')
    console.log('   - SharePoint API issues')
    console.log('   - Environment configuration problems')
  } else {
    console.log('✅ All recent schedules have SharePoint folders')
  }
}

checkSchedules()
  .then(() => {
    console.log('🏁 Check completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Check failed:', error)
    process.exit(1)
  })
