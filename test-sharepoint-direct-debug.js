#!/usr/bin/env node

/**
 * Test script to debug SharePoint folder creation directly
 * This bypasses the UI and tests the SharePoint service directly
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testSharePointDirect() {
  console.log('🧪 Testing SharePoint Folder Creation Directly')
  console.log('=' .repeat(50))

  try {
    // Step 1: Create a test schedule first
    console.log('\n1️⃣ Creating a test schedule...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select(`
        id, 
        custom_id, 
        name,
        client_id,
        clients!inner (
          id,
          custom_id,
          name
        )
      `)
      .limit(1)

    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)

    if (!projects?.length || !users?.length) {
      console.log('❌ No projects or users found')
      return
    }

    const project = projects[0]
    const user = users[0]
    
    console.log(`   📋 Using project: ${project.custom_id} ${project.name}`)
    console.log(`   🏢 Client: ${project.clients.custom_id} ${project.clients.name}`)

    // Create schedule directly via database function
    const { data: schedule, error: scheduleError } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: project.id,
      p_scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      p_scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      p_pilot_id: user.id,
      p_amount: 1500,
      p_location: 'Test Location for SharePoint Debug',
      p_google_maps_link: 'https://maps.google.com',
      p_notes: 'Test schedule for SharePoint folder creation debugging',
      p_is_recurring: false,
      p_recurring_pattern: null,
      p_is_outsourced: false,
      p_vendors: []
    })

    if (scheduleError) {
      console.log('❌ Error creating schedule:', scheduleError)
      return
    }

    console.log(`   ✅ Schedule created: ${schedule.custom_id} (ID: ${schedule.id})`)

    // Step 2: Test SharePoint folder creation via API
    console.log('\n2️⃣ Testing SharePoint folder creation via API...')
    
    try {
      const response = await fetch('http://localhost:3000/api/test-sharepoint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scheduleId: schedule.id
        })
      })

      const result = await response.json()
      
      if (result.success) {
        console.log('   ✅ SharePoint API call succeeded')
        
        // Check database for folder details
        const { data: updatedSchedule } = await supabase
          .from('schedules')
          .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
          .eq('id', schedule.id)
          .single()

        if (updatedSchedule?.sharepoint_folder_id) {
          console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
          console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
          console.log(`   📤 Share Link: ${updatedSchedule.sharepoint_share_link}`)
        } else {
          console.log('   ❌ Folder details not saved to database')
        }
      } else {
        console.log('   ❌ SharePoint API call failed:', result.message)
        if (result.error) {
          console.log('   🔍 Error details:', result.error)
        }
      }
    } catch (apiError) {
      console.log('   ❌ Error calling SharePoint API:', apiError.message)
    }

    // Step 3: Test background job execution
    console.log('\n3️⃣ Testing background job execution...')
    
    try {
      const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folder-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entityType: 'schedule',
          entityId: schedule.id,
          payload: {
            scheduleId: schedule.id,
            customId: schedule.custom_id,
            scheduledDate: schedule.scheduled_date
          }
        })
      })

      const result = await response.json()
      
      if (result.success) {
        console.log('   ✅ Background job execution succeeded')
        console.log(`   📋 Job ID: ${result.jobId}`)
      } else {
        console.log('   ❌ Background job execution failed:', result.message)
      }
    } catch (jobError) {
      console.log('   ❌ Error executing background job:', jobError.message)
    }

    // Step 4: Environment diagnostics
    console.log('\n4️⃣ Environment Diagnostics...')
    console.log(`   🔧 SHAREPOINT_FOLDER_CREATION_MODE: ${process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'not set'}`)
    console.log(`   🔑 MICROSOFT_CLIENT_ID: ${process.env.MICROSOFT_CLIENT_ID ? 'set' : 'not set'}`)
    console.log(`   🔐 MICROSOFT_CLIENT_SECRET: ${process.env.MICROSOFT_CLIENT_SECRET ? 'set' : 'not set'}`)
    console.log(`   🏢 MICROSOFT_TENANT_ID: ${process.env.MICROSOFT_TENANT_ID ? 'set' : 'not set'}`)
    console.log(`   🌐 NODE_ENV: ${process.env.NODE_ENV || 'not set'}`)

    // Step 5: Test Microsoft Graph access token
    console.log('\n5️⃣ Testing Microsoft Graph Access Token...')
    
    try {
      const response = await fetch('http://localhost:3000/api/test-graph-token', {
        method: 'GET'
      })

      if (response.ok) {
        const result = await response.json()
        console.log('   ✅ Microsoft Graph token test succeeded')
        if (result.tokenInfo) {
          console.log(`   🔑 Token expires: ${result.tokenInfo.expires_on ? new Date(result.tokenInfo.expires_on * 1000).toISOString() : 'unknown'}`)
        }
      } else {
        console.log('   ❌ Microsoft Graph token test failed')
        const errorText = await response.text()
        console.log(`   🔍 Response: ${errorText}`)
      }
    } catch (tokenError) {
      console.log('   ❌ Error testing Microsoft Graph token:', tokenError.message)
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testSharePointDirect()
