// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

async function testClientSideSharePoint() {
  try {
    console.log('🧪 Testing client-side SharePoint call (simulating createWithVendors)...\n')
    
    // Create a test schedule first
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    // Get a valid project
    const { data: projects } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1)
    
    const project = projects[0]
    console.log(`✅ Using project: ${project.custom_id}`)
    
    // Get a valid pilot
    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
    
    const pilot = users[0]
    console.log(`✅ Using pilot: ${pilot.name}`)
    
    // Create schedule
    const scheduleData = {
      p_project_id: project.id,
      p_scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      p_scheduled_end_date: new Date(Date.now() + 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000).toISOString(),
      p_pilot_id: pilot.id,
      p_amount: 5000,
      p_location: 'Test Location for Client-Side Test',
      p_google_maps_link: 'https://maps.google.com',
      p_notes: 'Test schedule for client-side SharePoint test',
      p_is_recurring: false,
      p_recurring_pattern: null,
      p_is_outsourced: false,
      p_vendors: []
    }
    
    const { data: newSchedule, error: scheduleError } = await supabase
      .rpc('create_schedule_with_vendors', scheduleData)
    
    if (scheduleError) {
      console.log('❌ Error creating schedule:', scheduleError)
      return
    }
    
    console.log(`✅ Schedule created: ${newSchedule.custom_id} (ID: ${newSchedule.id})`)
    
    // Now simulate the EXACT same SharePoint call as in createWithVendors
    console.log('\n🔄 Simulating exact SharePoint call from createWithVendors...')
    
    try {
      // This is the exact code from createWithVendors method
      console.log('   📦 Attempting dynamic import of SharePointService...')
      
      // Try the dynamic import (this might fail in Node.js context)
      const { SharePointService } = await import('./src/lib/sharepoint-service.js')
      console.log('   ✅ Dynamic import successful')
      
      console.log('   🔄 Calling ensureScheduleFolder...')
      await SharePointService.ensureScheduleFolder(newSchedule.id)
      console.log('   ✅ ensureScheduleFolder completed successfully')
      
    } catch (folderError) {
      console.log('   ❌ SharePoint folder creation error:', folderError.message)
      console.log('   📋 Error type:', folderError.constructor.name)
      console.log('   📋 Error code:', folderError.code)
      console.log('   📋 Full error:', folderError)
      
      // Try alternative approaches
      console.log('\n🔄 Trying alternative import methods...')
      
      try {
        // Try with .ts extension
        const { SharePointService: SharePointService2 } = await import('./src/lib/sharepoint-service.ts')
        console.log('   ✅ .ts import successful')
        await SharePointService2.ensureScheduleFolder(newSchedule.id)
        console.log('   ✅ Alternative method successful')
      } catch (altError) {
        console.log('   ❌ Alternative method failed:', altError.message)
        
        try {
          // Try with full path
          const path = require('path')
          const fullPath = path.resolve(__dirname, 'src/lib/sharepoint-service.ts')
          console.log('   🔄 Trying full path:', fullPath)
          const { SharePointService: SharePointService3 } = await import(fullPath)
          console.log('   ✅ Full path import successful')
          await SharePointService3.ensureScheduleFolder(newSchedule.id)
          console.log('   ✅ Full path method successful')
        } catch (fullPathError) {
          console.log('   ❌ Full path method failed:', fullPathError.message)
        }
      }
    }
    
    // Check if folder was created
    console.log('\n📁 Checking if SharePoint folder was created...')
    
    const { data: finalCheck } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', newSchedule.id)
      .single()
    
    if (finalCheck && finalCheck.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created!')
      console.log(`   📁 Folder ID: ${finalCheck.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${finalCheck.sharepoint_folder_url}`)
    } else {
      console.log('   ❌ SharePoint folder not created')
      
      // Try manual API call as fallback
      console.log('\n🔄 Trying manual API call as fallback...')
      try {
        const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          console.log('   ✅ Manual API call successful')
          
          // Check again
          const { data: secondCheck } = await supabase
            .from('schedules')
            .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
            .eq('id', newSchedule.id)
            .single()
          
          if (secondCheck && secondCheck.sharepoint_folder_id) {
            console.log('   ✅ SharePoint folder created after manual API call!')
            console.log(`   📁 Folder ID: ${secondCheck.sharepoint_folder_id}`)
            console.log(`   🔗 Folder URL: ${secondCheck.sharepoint_folder_url}`)
          }
        } else {
          console.log('   ❌ Manual API call failed:', response.status)
        }
      } catch (apiError) {
        console.log('   ❌ Manual API call error:', apiError.message)
      }
    }
    
    console.log('\n🎯 Test Complete!')
    console.log(`\n💡 You can clean up by deleting schedule ${newSchedule.custom_id} if needed.`)
    
  } catch (error) {
    console.error('❌ Test error:', error)
  }
}

testClientSideSharePoint()