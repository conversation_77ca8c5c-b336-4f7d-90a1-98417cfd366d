import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY // Use service role key to bypass RLS

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function setupTestData() {
  console.log('🚀 Setting up test data for schedule creation testing...\n')

  try {
    // 1. Create a test client
    console.log('🏢 Step 1: Creating test client...')
    
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .insert({
        name: 'Test Client Corp',
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Test Street, Test City, TC 12345',
        client_type: 'corporate'
      })
      .select()
      .single()

    if (clientError) {
      throw new Error(`Failed to create client: ${clientError.message}`)
    }

    console.log(`   ✅ Client created: ${client.name} (${client.id})`)

    // 2. Create a test user (pilot)
    console.log('\n👨‍✈️ Step 2: Creating test pilot...')
    
    const { data: pilot, error: pilotError } = await supabase
      .from('users')
      .insert({
        name: 'Test Pilot',
        email: '<EMAIL>',
        role: 'pilot'
      })
      .select()
      .single()

    if (pilotError) {
      throw new Error(`Failed to create pilot: ${pilotError.message}`)
    }

    console.log(`   ✅ Pilot created: ${pilot.name} (${pilot.id})`)

    // 3. Create a test project
    console.log('\n🏗️ Step 3: Creating test project...')
    
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        name: 'Test Project Alpha',
        client_id: client.id,
        description: 'Test project for SharePoint folder creation testing',
        status: 'active',
        budget: 50000
      })
      .select()
      .single()

    if (projectError) {
      throw new Error(`Failed to create project: ${projectError.message}`)
    }

    console.log(`   ✅ Project created: ${project.name} (${project.id})`)

    // 4. Verify the setup
    console.log('\n✅ Step 4: Verifying test data setup...')
    
    const { data: verifyClient } = await supabase
      .from('clients')
      .select('id, name')
      .eq('id', client.id)
      .single()
    
    const { data: verifyPilot } = await supabase
      .from('users')
      .select('id, name, role')
      .eq('id', pilot.id)
      .single()
    
    const { data: verifyProject } = await supabase
      .from('projects')
      .select('id, name, client_id')
      .eq('id', project.id)
      .single()

    console.log(`   ✅ Client verified: ${verifyClient?.name}`)
    console.log(`   ✅ Pilot verified: ${verifyPilot?.name} (${verifyPilot?.role})`)
    console.log(`   ✅ Project verified: ${verifyProject?.name}`)

    console.log('\n🎯 Test data setup complete!')
    console.log('\n📋 Summary:')
    console.log(`   Client ID: ${client.id}`)
    console.log(`   Pilot ID: ${pilot.id}`)
    console.log(`   Project ID: ${project.id}`)
    console.log('\n💡 You can now test schedule creation with SharePoint folder functionality!')

    return {
      client,
      pilot,
      project
    }

  } catch (error) {
    console.error('❌ Test data setup failed:', error.message)
    console.error('Stack trace:', error.stack)
    process.exit(1)
  }
}

setupTestData()