#!/usr/bin/env node

/**
 * Test script to simulate the exact frontend schedule creation flow
 * This tests the schedulesApi.createWithVendors method directly
 */

require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testExactFrontendFlow() {
  console.log('🧪 Testing Exact Frontend Schedule Creation Flow...')
  
  try {
    // Step 1: Get test data (use the same approach as working tests)
    console.log('\n1️⃣ Finding test data...')
    
    // Use the project ID that we know exists from previous tests
    const project = {
      id: 'CYMPR-25-010',
      custom_id: 'CYMPR-25-010',
      name: 'fourth'
    }
    console.log(`   ✅ Using project: ${project.name} (${project.custom_id})`)
    
    // Step 2: Create schedule using the exact frontend API call
    console.log('\n2️⃣ Creating schedule via frontend API (schedulesApi.createWithVendors)...')
    
    // Import the API function exactly as the frontend does
    const { schedulesApi } = require('./src/lib/api.ts')
    
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date().toISOString().split('T')[0],
      scheduled_end_date: new Date().toISOString().split('T')[0],
      pilot_id: null,
      amount: 5000,
      location: 'Test Location for Frontend Flow',
      google_maps_link: null,
      notes: 'Test schedule created via exact frontend flow',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }
    
    const vendors = [] // No vendors for this test
    
    console.log('   📝 Schedule data:', {
      project_id: scheduleData.project_id,
      scheduled_date: scheduleData.scheduled_date,
      location: scheduleData.location,
      amount: scheduleData.amount
    })
    
    // This is the exact call the frontend makes
    const schedule = await schedulesApi.createWithVendors(scheduleData, vendors)
    
    console.log(`   ✅ Schedule created: ${schedule.custom_id}`)
    console.log(`   🆔 Schedule ID: ${schedule.id}`)
    
    // Step 3: Wait for background job to process
    console.log('\n3️⃣ Waiting for background job to process...')
    await new Promise(resolve => setTimeout(resolve, 10000))
    
    // Step 4: Check if SharePoint folder was created
    console.log('\n4️⃣ Checking SharePoint folder creation...')
    
    const { data: updatedSchedule } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', schedule.id)
      .single()
    
    if (updatedSchedule.sharepoint_folder_id && updatedSchedule.sharepoint_folder_url) {
      console.log('   ✅ SharePoint folder created successfully!')
      console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
      if (updatedSchedule.sharepoint_share_link) {
        console.log(`   🔗 Share Link: ${updatedSchedule.sharepoint_share_link}`)
      }
    } else {
      console.log('   ⚠️ SharePoint folder not created yet or failed')
      console.log('   📊 Current schedule state:', {
        sharepoint_folder_id: updatedSchedule.sharepoint_folder_id,
        sharepoint_folder_url: updatedSchedule.sharepoint_folder_url,
        sharepoint_share_link: updatedSchedule.sharepoint_share_link
      })
    }
    
    console.log('\n✅ Exact frontend flow test completed!')
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message)
    if (error.stack) {
      console.error('Stack trace:', error.stack)
    }
  }
}

// Run the test
testExactFrontendFlow()