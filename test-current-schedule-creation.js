import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testCurrentScheduleCreation() {
  console.log('🧪 Testing current schedule creation and SharePoint folder creation...\n')

  try {
    // 1. Check if SharePoint API is working
    console.log('🔍 Step 1: Testing SharePoint API endpoint...')
    
    try {
      const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        console.log('   ✅ SharePoint API is working')
      } else {
        console.log(`   ❌ SharePoint API failed: ${response.status}`)
        const errorText = await response.text()
        console.log(`   Error: ${errorText}`)
      }
    } catch (apiError) {
      console.log('   ❌ SharePoint API connection failed:', apiError.message)
    }

    // 2. Check recent schedules and their SharePoint folder status
    console.log('\n🔍 Step 2: Checking recent schedules...')
    
    const { data: recentSchedules, error: scheduleError } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        created_at,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link,
        project:projects(name, client:clients(name))
      `)
      .order('created_at', { ascending: false })
      .limit(5)

    if (scheduleError) {
      throw new Error(`Failed to fetch schedules: ${scheduleError.message}`)
    }

    if (!recentSchedules || recentSchedules.length === 0) {
      console.log('   ❌ No schedules found in database')
      return
    }

    console.log(`   ✅ Found ${recentSchedules.length} recent schedules`)
    
    recentSchedules.forEach((schedule, index) => {
      const hasSharePoint = !!schedule.sharepoint_folder_id
      const status = hasSharePoint ? '✅' : '❌'
      console.log(`   ${status} ${schedule.custom_id} - ${hasSharePoint ? 'Has SharePoint folder' : 'Missing SharePoint folder'}`)
    })

    // 3. Find a valid project and pilot for testing
    console.log('\n🔍 Step 3: Finding valid project and pilot...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select('id, name, client_id')
      .limit(1)
    
    const { data: pilots } = await supabase
      .from('users')
      .select('id, name')
      .eq('role', 'pilot')
      .limit(1)
    
    if (!projects || projects.length === 0) {
      console.log('   ❌ No projects found - cannot test schedule creation')
      return
    }
    
    if (!pilots || pilots.length === 0) {
      console.log('   ❌ No pilots found - cannot test schedule creation')
      return
    }

    const project = projects[0]
    const pilot = pilots[0]
    
    console.log(`   ✅ Project: ${project.name}`)
    console.log(`   ✅ Pilot: ${pilot.name}`)

    // 4. Create a test schedule
    console.log('\n🔍 Step 4: Creating test schedule...')
    
    const scheduleData = {
      project_id: project.id,
      pilot_id: pilot.id,
      scheduled_date: new Date().toISOString().split('T')[0],
      amount: 1500,
      location: 'Test Location - Current Test',
      notes: 'Test schedule to verify current SharePoint folder creation'
    }

    const { data: newSchedule, error: createError } = await supabase.rpc('create_schedule_with_vendors', {
      p_schedule_data: scheduleData,
      p_vendors: []
    })

    if (createError) {
      throw new Error(`Schedule creation failed: ${createError.message}`)
    }

    console.log(`   ✅ Schedule created: ${newSchedule.custom_id} (${newSchedule.id})`)

    // 5. Wait and check if SharePoint folder was created
    console.log('\n🔍 Step 5: Checking SharePoint folder creation...')
    
    // Wait a few seconds for any async operations
    console.log('   ⏳ Waiting 5 seconds for SharePoint folder creation...')
    await new Promise(resolve => setTimeout(resolve, 5000))

    const { data: updatedSchedule } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', newSchedule.id)
      .single()

    if (updatedSchedule?.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created automatically!')
      console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
      if (updatedSchedule.sharepoint_share_link) {
        console.log(`   🔗 Share Link: ${updatedSchedule.sharepoint_share_link}`)
      }
    } else {
      console.log('   ❌ SharePoint folder NOT created automatically')
      
      // Try manual trigger
      console.log('\n🔍 Step 6: Testing manual SharePoint folder creation...')
      
      const manualResponse = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (manualResponse.ok) {
        console.log('   ✅ Manual SharePoint API call successful')
        
        // Check again after manual trigger
        const { data: finalSchedule } = await supabase
          .from('schedules')
          .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
          .eq('id', newSchedule.id)
          .single()
        
        if (finalSchedule?.sharepoint_folder_id) {
          console.log('   ✅ SharePoint folder created after manual trigger!')
          console.log(`   📁 Folder ID: ${finalSchedule.sharepoint_folder_id}`)
          console.log(`   🔗 Folder URL: ${finalSchedule.sharepoint_folder_url}`)
        } else {
          console.log('   ❌ SharePoint folder still not created after manual trigger')
        }
      } else {
        console.log('   ❌ Manual SharePoint API call failed')
      }
    }

    console.log(`\n🎯 Test Complete!`)
    console.log(`📋 Created schedule: ${newSchedule.custom_id}`)
    console.log('💡 You can clean up by deleting this test schedule if needed.')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('Stack trace:', error.stack)
  }
}

testCurrentScheduleCreation()