#!/usr/bin/env node

import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('Environment check:')
console.log('- Supabase URL:', supabaseUrl ? 'loaded' : 'missing')
console.log('- Service Key:', supabaseServiceKey ? 'loaded' : 'missing')

async function testBackgroundJobsSystem() {
  console.log('🧪 Testing background jobs system via API...\n')

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // 1. Initialize background jobs via API
    console.log('🔧 Step 1: Initializing background jobs system...')
    try {
      const initResponse = await fetch('http://localhost:3000/api/init')
      if (initResponse.ok) {
        const initResult = await initResponse.json()
        console.log('   ✅ Background jobs initialized:', initResult.status)
      } else {
        console.log('   ❌ Failed to initialize background jobs:', initResponse.status)
      }
    } catch (initError) {
      console.log('   ❌ Error initializing background jobs:', initError.message)
    }

    // 2. Get a project to use for testing
    console.log('\n🔍 Step 2: Finding a project for testing...')
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1)

    if (projectError || !projects?.length) {
      throw new Error('No projects found for testing')
    }

    const project = projects[0]
    console.log(`   📋 Using project: ${project.custom_id} - ${project.name}`)

    // 3. Create a test schedule using the PostgreSQL function
    console.log('\n🎯 Step 3: Creating test schedule...')
    const { data: schedule, error: scheduleError } = await supabase
      .rpc('create_schedule_with_vendors', {
        p_project_id: project.id,
        p_scheduled_date: '2025-01-20',
        p_scheduled_end_date: '2025-01-20',
        p_pilot_id: null,
        p_amount: 1000,
        p_location: 'Test Location for Background Jobs',
        p_google_maps_link: 'https://maps.google.com',
        p_notes: 'Testing background jobs system',
        p_is_recurring: false,
        p_recurring_pattern: null,
        p_is_outsourced: false,
        p_vendors: []
      })

    if (scheduleError) {
      throw new Error(`Failed to create schedule: ${scheduleError.message}`)
    }

    console.log(`   ✅ Schedule created: ${schedule.custom_id}`)

    // 4. Manually trigger SharePoint folder creation via API
    console.log('\n🔄 Step 4: Manually triggering SharePoint folder creation...')
    try {
      const sharePointResponse = await fetch('http://localhost:3000/api/ensure-sharepoint-folder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ scheduleId: schedule.id })
      })

      if (sharePointResponse.ok) {
        const sharePointResult = await sharePointResponse.json()
        console.log('   ✅ SharePoint API response:', sharePointResult.message)
      } else {
        const errorText = await sharePointResponse.text()
        console.log('   ❌ SharePoint API failed:', sharePointResponse.status, errorText)
      }
    } catch (sharePointError) {
      console.log('   ❌ Error calling SharePoint API:', sharePointError.message)
    }

    // 5. Check background jobs status via API
    console.log('\n📊 Step 5: Checking background jobs status...')
    try {
      const jobsResponse = await fetch('http://localhost:3000/api/background-jobs')
      if (jobsResponse.ok) {
        const jobsResult = await jobsResponse.json()
        console.log(`   📋 Background jobs: ${jobsResult.jobs?.length || 0} jobs in queue`)
        if (jobsResult.jobs?.length > 0) {
          jobsResult.jobs.forEach((job, index) => {
            console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status}`)
          })
        }
      } else {
        console.log('   ❌ Failed to get background jobs:', jobsResponse.status)
      }
    } catch (jobsError) {
      console.log('   ❌ Error getting background jobs:', jobsError.message)
    }

    // 6. Wait a moment and check if SharePoint folder was created
    console.log('\n⏳ Step 6: Waiting for SharePoint processing...')
    await new Promise(resolve => setTimeout(resolve, 5000))

    console.log('\n🔍 Step 7: Checking SharePoint folder creation...')
    const { data: updatedSchedule, error: fetchError } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', schedule.id)
      .single()

    if (fetchError) {
      console.log(`   ❌ Error fetching updated schedule: ${fetchError.message}`)
    } else {
      console.log(`   📋 Schedule: ${updatedSchedule.custom_id}`)
      console.log(`   📋 SharePoint Folder ID: ${updatedSchedule.sharepoint_folder_id || 'null'}`)
      console.log(`   📋 SharePoint Folder URL: ${updatedSchedule.sharepoint_folder_url || 'null'}`)
      console.log(`   📋 SharePoint Share Link: ${updatedSchedule.sharepoint_share_link || 'null'}`)

      if (updatedSchedule.sharepoint_folder_id) {
        console.log('   ✅ SharePoint folder was created successfully!')
      } else {
        console.log('   ❌ SharePoint folder was NOT created')
      }
    }

    // 7. Cleanup test schedule
    console.log('\n🧹 Step 7: Cleaning up test schedule...')
    const { error: deleteError } = await supabase
      .from('schedules')
      .delete()
      .eq('id', schedule.id)

    if (deleteError) {
      console.log(`   ❌ Error deleting test schedule: ${deleteError.message}`)
    } else {
      console.log('   ✅ Test schedule cleaned up successfully')
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('📋 Full error:', error)
  }
}

testBackgroundJobsSystem()