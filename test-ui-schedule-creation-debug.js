import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testUIScheduleCreation() {
  console.log('🧪 Testing UI Schedule Creation Issue')
  console.log('=====================================')

  try {
    // Step 1: Get a project to use
    console.log('\n📋 Step 1: Getting a project...')
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, client:clients(name)')
      .limit(1)

    if (projectsError) throw projectsError
    if (!projects || projects.length === 0) {
      console.log('❌ No projects found')
      return
    }

    const project = projects[0]
    console.log(`   ✅ Using project: ${project.name} (${project.client?.name})`)

    // Step 2: Simulate the exact UI call using schedulesApi.createWithVendors
    console.log('\n🔄 Step 2: Simulating UI schedule creation...')
    
    // Import the API exactly as the UI does
    const { schedulesApi } = await import('./src/lib/api.ts')
    
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      scheduled_end_date: null,
      pilot_id: null,
      amount: 1500,
      location: 'Test Location - UI Debug',
      google_maps_link: '',
      notes: 'Testing UI schedule creation issue',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }

    console.log('   📝 Schedule data:', {
      project_id: scheduleData.project_id,
      scheduled_date: scheduleData.scheduled_date,
      amount: scheduleData.amount,
      location: scheduleData.location
    })

    // This is the exact call the UI makes
    const result = await schedulesApi.createWithVendors(scheduleData, [])
    
    console.log(`   ✅ Schedule created: ${result.custom_id} (ID: ${result.id})`)

    // Step 3: Check if SharePoint folder was created immediately
    console.log('\n📁 Step 3: Checking SharePoint folder status...')
    console.log('   📋 Initial SharePoint status:', {
      folder_id: result.sharepoint_folder_id,
      folder_url: result.sharepoint_folder_url,
      share_link: result.sharepoint_share_link
    })

    // Step 4: Wait a moment and re-fetch to check for updates
    console.log('\n⏳ Step 4: Waiting 3 seconds and re-checking...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    const updatedSchedule = await schedulesApi.getById(result.id)
    console.log('   📋 Updated SharePoint status:', {
      folder_id: updatedSchedule.sharepoint_folder_id,
      folder_url: updatedSchedule.sharepoint_folder_url,
      share_link: updatedSchedule.sharepoint_share_link
    })

    // Step 5: Check background jobs
    console.log('\n🔧 Step 5: Checking background jobs...')
    try {
      const jobsResponse = await fetch('http://localhost:3001/api/background-jobs')
      if (jobsResponse.ok) {
        const jobsResult = await jobsResponse.json()
        console.log(`   📋 Background jobs: ${jobsResult.length || 0} jobs in queue`)
        if (jobsResult.length > 0) {
          jobsResult.forEach((job, index) => {
            console.log(`   ${index + 1}. ${job.id} - ${job.type} - ${job.status} - ${job.entityType}:${job.entityId}`)
          })
        }
      } else {
        console.log('   ❌ Failed to get background jobs:', jobsResponse.status)
      }
    } catch (jobsError) {
      console.log('   ❌ Error getting background jobs:', jobsError.message)
    }

    // Step 6: Check environment configuration
    console.log('\n⚙️ Step 6: Checking environment configuration...')
    console.log('   📋 SHAREPOINT_FOLDER_CREATION_MODE:', process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'undefined (defaults to background-with-fallback)')
    console.log('   📋 NODE_ENV:', process.env.NODE_ENV)

    // Step 7: Test direct SharePoint folder creation
    console.log('\n🔧 Step 7: Testing direct SharePoint folder creation...')
    try {
      const response = await fetch('http://localhost:3001/api/ensure-sharepoint-folder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ scheduleId: result.id })
      })

      if (response.ok) {
        const ensureResult = await response.json()
        console.log('   ✅ Direct SharePoint creation response:', ensureResult)
      } else {
        const errorText = await response.text()
        console.log('   ❌ Direct SharePoint creation failed:', response.status, errorText)
      }
    } catch (ensureError) {
      console.log('   ❌ Error calling direct SharePoint creation:', ensureError.message)
    }

    // Step 8: Final status check
    console.log('\n📊 Step 8: Final status check...')
    const finalSchedule = await schedulesApi.getById(result.id)
    console.log('   📋 Final SharePoint status:', {
      folder_id: finalSchedule.sharepoint_folder_id,
      folder_url: finalSchedule.sharepoint_folder_url,
      share_link: finalSchedule.sharepoint_share_link
    })

    if (finalSchedule.sharepoint_folder_id) {
      console.log('\n🎉 SUCCESS: SharePoint folder was created!')
    } else {
      console.log('\n❌ ISSUE CONFIRMED: SharePoint folder was NOT created via UI')
    }

    // Step 9: Cleanup
    console.log('\n🧹 Step 9: Cleaning up test schedule...')
    const { error: deleteError } = await supabase
      .from('schedules')
      .delete()
      .eq('id', result.id)

    if (deleteError) {
      console.log('   ❌ Error deleting test schedule:', deleteError)
    } else {
      console.log('   ✅ Test schedule cleaned up successfully')
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testUIScheduleCreation()