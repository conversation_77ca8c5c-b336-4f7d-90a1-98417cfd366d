#!/usr/bin/env node

/**
 * Test script to test the new /api/schedules endpoint
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testNewAPIEndpoint() {
  console.log('🧪 Testing New /api/schedules Endpoint')
  console.log('=' .repeat(40))

  try {
    // Step 1: Get test data
    console.log('\n1️⃣ Getting test data...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select(`
        id, 
        custom_id, 
        name,
        client_id,
        clients!inner (
          id,
          custom_id,
          name
        )
      `)
      .limit(1)

    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)

    if (!projects?.length || !users?.length) {
      console.log('❌ No projects or users found')
      return
    }

    const project = projects[0]
    const user = users[0]
    
    console.log(`   📋 Using project: ${project.custom_id} ${project.name}`)
    console.log(`   🏢 Client: ${project.clients.custom_id} ${project.clients.name}`)

    // Step 2: Call the new API endpoint
    console.log('\n2️⃣ Calling /api/schedules endpoint...')
    
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      pilot_id: user.id,
      amount: 3000,
      location: 'Test Location via New API Endpoint',
      google_maps_link: 'https://maps.google.com',
      notes: 'Test schedule created via new /api/schedules endpoint',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }

    const vendors = []

    const response = await fetch('http://localhost:3000/api/schedules', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        schedule: scheduleData,
        vendors: vendors
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.log(`   ❌ API call failed: ${response.status} ${response.statusText}`)
      console.log(`   🔍 Error response: ${errorText}`)
      return
    }

    const result = await response.json()
    console.log(`   ✅ Schedule created via API: ${result.custom_id} (ID: ${result.id})`)

    // Step 3: Check SharePoint folder creation immediately
    console.log('\n3️⃣ Checking SharePoint folder creation...')
    
    const { data: immediateCheck } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', result.id)
      .single()

    if (immediateCheck?.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created immediately!')
      console.log(`   📁 Folder ID: ${immediateCheck.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${immediateCheck.sharepoint_folder_url}`)
      console.log(`   📤 Share Link: ${immediateCheck.sharepoint_share_link}`)
    } else {
      console.log('   ⏳ SharePoint folder not created immediately, waiting...')
      
      // Wait a bit for background processing
      await new Promise(resolve => setTimeout(resolve, 5000))
      
      const { data: delayedCheck } = await supabase
        .from('schedules')
        .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
        .eq('id', result.id)
        .single()

      if (delayedCheck?.sharepoint_folder_id) {
        console.log('   ✅ SharePoint folder created after delay!')
        console.log(`   📁 Folder ID: ${delayedCheck.sharepoint_folder_id}`)
        console.log(`   🔗 Folder URL: ${delayedCheck.sharepoint_folder_url}`)
        console.log(`   📤 Share Link: ${delayedCheck.sharepoint_share_link}`)
      } else {
        console.log('   ❌ SharePoint folder still not created')
      }
    }

    // Step 4: Verify the schedule was created correctly
    console.log('\n4️⃣ Verifying schedule details...')
    
    const { data: scheduleCheck } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        project_id,
        scheduled_date,
        scheduled_end_date,
        pilot_id,
        amount,
        location,
        notes,
        is_outsourced,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link
      `)
      .eq('id', result.id)
      .single()

    if (scheduleCheck) {
      console.log('   ✅ Schedule details verified:')
      console.log(`      Custom ID: ${scheduleCheck.custom_id}`)
      console.log(`      Project ID: ${scheduleCheck.project_id}`)
      console.log(`      Scheduled Date: ${scheduleCheck.scheduled_date}`)
      console.log(`      Amount: ${scheduleCheck.amount}`)
      console.log(`      Location: ${scheduleCheck.location}`)
      console.log(`      SharePoint Folder: ${scheduleCheck.sharepoint_folder_id ? 'Created' : 'Not Created'}`)
    } else {
      console.log('   ❌ Could not verify schedule details')
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testNewAPIEndpoint()
