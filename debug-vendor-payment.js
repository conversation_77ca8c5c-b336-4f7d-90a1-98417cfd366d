// Debug script to check vendor payment data
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function debugVendorPayment() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography vendor ID
  
  console.log('🔍 Debugging vendor payment for vendor:', vendorId)
  
  // 1. Check vendor details
  const { data: vendor, error: vendorError } = await supabase
    .from('outsourcing_vendors')
    .select('*')
    .eq('id', vendorId)
    .single()
    
  if (vendorError) {
    console.error('❌ Error fetching vendor:', vendorError)
    return
  }
  
  console.log('✅ Vendor found:', vendor.name)
  
  // 2. Check schedule_vendors junction table
  const { data: scheduleVendors, error: svError } = await supabase
    .from('schedule_vendors')
    .select('*')
    .eq('vendor_id', vendorId)
    
  if (svError) {
    console.error('❌ Error fetching schedule vendors:', svError)
    return
  }
  
  console.log('📋 Schedule vendors entries:', scheduleVendors.length)
  scheduleVendors.forEach(sv => {
    console.log(`  - Schedule ${sv.schedule_id}: Cost ₹${sv.cost}`)
  })
  
  // 2b. Check all outsourced schedules to see what's available
  const { data: allOutsourcedSchedules, error: allSchedulesError } = await supabase
    .from('schedules')
    .select(`
      id,
      project_id,
      scheduled_date,
      is_outsourced,
      project:projects(name)
    `)
    .eq('is_outsourced', true)
    .order('scheduled_date', { ascending: false })
    
  if (allSchedulesError) {
    console.error('❌ Error fetching all outsourced schedules:', allSchedulesError)
  } else {
    console.log('📋 All outsourced schedules:', allOutsourcedSchedules.length)
    allOutsourcedSchedules.forEach(schedule => {
      console.log(`  - Schedule ${schedule.id} (${schedule.project?.name}) - ${schedule.scheduled_date}`)
    })
  }
  
  // 3. Check projects and their payment status
  const scheduleIds = scheduleVendors.map(sv => sv.schedule_id)
  
  if (scheduleIds.length === 0) {
    console.log('⚠️ No schedules found for this vendor')
    console.log('💡 This vendor needs to be linked to schedules in the schedule_vendors table')
    return
  }
  
  const { data: schedules, error: schedulesError } = await supabase
    .from('schedules')
    .select(`
      id,
      project_id,
      scheduled_date,
      project:projects(
        id,
        name,
        vendor_payment_status,
        vendor_payment_amount
      )
    `)
    .in('id', scheduleIds)
    
  if (schedulesError) {
    console.error('❌ Error fetching schedules:', schedulesError)
    return
  }
  
  console.log('📅 Schedules and project payment status:')
  schedules.forEach(schedule => {
    console.log(`  - Schedule ${schedule.id} (Project: ${schedule.project?.name})`)
    console.log(`    Payment Status: ${schedule.project?.vendor_payment_status || 'null'}`)
    console.log(`    Payment Amount: ₹${schedule.project?.vendor_payment_amount || 0}`)
  })
  
  // 4. Check expenses for each project
  const projectIds = [...new Set(schedules.map(s => s.project_id))]
  
  for (const projectId of projectIds) {
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .select('*')
      .eq('project_id', projectId)
      .eq('category', 'outsourcing')
      
    if (expensesError) {
      console.error(`❌ Error fetching expenses for project ${projectId}:`, expensesError)
      continue
    }
    
    const project = schedules.find(s => s.project_id === projectId)?.project
    console.log(`💰 Outsourcing expenses for project "${project?.name}" (${projectId}):`)
    
    if (expenses.length === 0) {
      console.log('  - No outsourcing expenses found')
    } else {
      expenses.forEach(expense => {
        console.log(`  - ₹${expense.amount} on ${expense.created_at} (${expense.description || 'No description'})`)
      })
      
      const totalExpenses = expenses.reduce((sum, exp) => sum + exp.amount, 0)
      console.log(`  - Total outsourcing expenses: ₹${totalExpenses}`)
    }
  }
  
  // 5. Check total costs vs payments
  const totalCost = scheduleVendors.reduce((sum, sv) => sum + (sv.cost || 0), 0)
  const totalPaid = schedules.reduce((sum, s) => sum + (s.project?.vendor_payment_amount || 0), 0)
  
  console.log('\n📊 Summary:')
  console.log(`Total cost owed to vendor: ₹${totalCost}`)
  console.log(`Total amount marked as paid: ₹${totalPaid}`)
  console.log(`Remaining amount: ₹${totalCost - totalPaid}`)
}

debugVendorPayment().catch(console.error)