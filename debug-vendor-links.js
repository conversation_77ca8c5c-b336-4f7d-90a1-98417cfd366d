const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://qxgfulribhhohmggyroq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU'
)

async function debugVendorLinks() {
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  console.log('🔍 DEBUGGING VENDOR LINKS')
  console.log('=========================')
  
  try {
    // 1. Check all vendors
    console.log('\n1️⃣ ALL VENDORS:')
    const { data: allVendors, error: vendorsError } = await supabase
      .from('outsourcing_vendors')
      .select('*')
    
    if (vendorsError) {
      console.error('❌ Vendors error:', vendorsError)
      return
    }
    
    console.log(`✅ Found ${allVendors.length} vendors:`)
    allVendors.forEach((vendor, index) => {
      console.log(`   ${index + 1}. ${vendor.name} (${vendor.id})`)
    })
    
    // 2. Check all schedule_vendors entries
    console.log('\n2️⃣ ALL SCHEDULE_VENDORS ENTRIES:')
    const { data: allScheduleVendors, error: svError } = await supabase
      .from('schedule_vendors')
      .select('*')
    
    if (svError) {
      console.error('❌ Schedule vendors error:', svError)
      return
    }
    
    console.log(`✅ Found ${allScheduleVendors.length} schedule-vendor links:`)
    allScheduleVendors.forEach((sv, index) => {
      console.log(`   ${index + 1}. Schedule: ${sv.schedule_id}, Vendor: ${sv.vendor_id}, Cost: ₹${sv.cost || 0}`)
    })
    
    // 3. Check for Arun photography specifically
    console.log('\n3️⃣ ARUN PHOTOGRAPHY LINKS:')
    const arunLinks = allScheduleVendors.filter(sv => sv.vendor_id === vendorId)
    console.log(`Found ${arunLinks.length} links for Arun photography`)
    
    if (arunLinks.length === 0) {
      console.log('❌ No links found for Arun photography!')
      console.log('This explains why the vendor shows ₹0 in the UI.')
    }
    
    // 4. Check if there are any expenses for Arun photography
    console.log('\n4️⃣ EXPENSES FOR ARUN PHOTOGRAPHY:')
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .select('*')
      .eq('vendor_id', vendorId)
    
    if (expensesError) {
      console.error('❌ Expenses error:', expensesError)
    } else {
      console.log(`✅ Found ${expenses.length} expenses for Arun photography:`)
      expenses.forEach((expense, index) => {
        console.log(`   ${index + 1}. Project: ${expense.project_id}, Amount: ₹${expense.amount}, Description: ${expense.description}`)
      })
    }
    
    // 5. Check all expenses with outsourcing category
    console.log('\n5️⃣ ALL OUTSOURCING EXPENSES:')
    const { data: allExpenses, error: allExpensesError } = await supabase
      .from('expenses')
      .select('*')
      .eq('category', 'outsourcing')
    
    if (allExpensesError) {
      console.error('❌ All expenses error:', allExpensesError)
    } else {
      console.log(`✅ Found ${allExpenses.length} outsourcing expenses:`)
      allExpenses.forEach((expense, index) => {
        console.log(`   ${index + 1}. Project: ${expense.project_id}, Vendor: ${expense.vendor_id || 'Not set'}, Amount: ₹${expense.amount}`)
      })
    }
    
    // 6. Check projects with vendor payment data
    console.log('\n6️⃣ PROJECTS WITH VENDOR PAYMENT DATA:')
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .not('vendor_payment_status', 'is', null)
    
    if (projectsError) {
      console.error('❌ Projects error:', projectsError)
    } else {
      console.log(`✅ Found ${projects.length} projects with vendor payment data:`)
      projects.forEach((project, index) => {
        console.log(`   ${index + 1}. ${project.name} - Status: ${project.vendor_payment_status}, Amount: ₹${project.vendor_payment_amount || 0}`)
      })
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

debugVendorLinks()