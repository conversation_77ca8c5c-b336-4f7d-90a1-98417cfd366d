#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkClientTypes() {
  console.log('🔍 Checking client types in database...');
  
  try {
    // Get all unique client types from the database
    const { data: clients, error } = await supabase
      .from('clients')
      .select('client_type')
      .not('client_type', 'is', null);
      
    if (error) {
      console.error('Error fetching clients:', error);
      return;
    }
    
    const uniqueClientTypes = [...new Set(clients.map(c => c.client_type))];
    
    console.log('📋 Unique client types in database:');
    uniqueClientTypes.forEach(type => {
      console.log(`  - "${type}"`);
    });
    
    // Check recent schedules and their client types
    const { data: schedules, error: schedError } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        created_at,
        project:projects!inner (
          id,
          name,
          client:clients!inner (
            client_type
          )
        )
      `)
      .order('created_at', { ascending: false })
      .limit(5);
      
    if (schedError) {
      console.error('Error fetching schedules:', schedError);
      return;
    }
    
    console.log('\n📅 Recent schedules and their client types:');
    schedules.forEach(schedule => {
      console.log(`  - Schedule ${schedule.custom_id || schedule.id}: "${schedule.project?.client?.client_type}"`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkClientTypes();