// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

async function testSharePointError() {
  try {
    console.log('🧪 Testing SharePoint folder creation with detailed error logging...\n')
    
    // Test 1: Check environment variables
    console.log('1️⃣ Checking environment variables...')
    const requiredEnvs = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY',
      'MICROSOFT_CLIENT_ID',
      'MICROSOFT_CLIENT_SECRET',
      'MICROSOFT_TENANT_ID'
    ]
    
    for (const env of requiredEnvs) {
      if (process.env[env]) {
        console.log(`   ✅ ${env}: Set`)
      } else {
        console.log(`   ❌ ${env}: Missing`)
      }
    }
    
    // Test 2: Try to import SharePointService
    console.log('\n2️⃣ Testing SharePointService import...')
    try {
      // Simulate the same import that happens in the API
      const path = require('path')
      const fs = require('fs')
      
      // Check if the file exists
      const sharePointServicePath = path.join(__dirname, 'src/lib/sharepoint-service.ts')
      if (fs.existsSync(sharePointServicePath)) {
        console.log('   ✅ SharePointService file exists')
      } else {
        console.log('   ❌ SharePointService file not found at:', sharePointServicePath)
      }
      
      // Try to read the file content to check for syntax issues
      const content = fs.readFileSync(sharePointServicePath, 'utf8')
      console.log('   ✅ SharePointService file readable')
      console.log(`   📄 File size: ${content.length} characters`)
      
    } catch (importError) {
      console.log('   ❌ SharePointService import error:', importError.message)
    }
    
    // Test 3: Try to create a schedule and capture the exact error
    console.log('\n3️⃣ Creating test schedule to capture SharePoint error...')
    
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    // Get a valid project
    const { data: projects } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1)
    
    if (!projects || projects.length === 0) {
      console.log('   ❌ No projects found')
      return
    }
    
    const project = projects[0]
    console.log(`   ✅ Using project: ${project.custom_id}`)
    
    // Get a valid pilot
    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
    
    if (!users || users.length === 0) {
      console.log('   ❌ No users found')
      return
    }
    
    const pilot = users[0]
    console.log(`   ✅ Using pilot: ${pilot.name}`)
    
    // Create schedule
    const scheduleData = {
      p_project_id: project.id,
      p_scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      p_scheduled_end_date: new Date(Date.now() + 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000).toISOString(),
      p_pilot_id: pilot.id,
      p_amount: 5000,
      p_location: 'Test Location for Error Testing',
      p_google_maps_link: 'https://maps.google.com',
      p_notes: 'Test schedule for error testing',
      p_is_recurring: false,
      p_recurring_pattern: null,
      p_is_outsourced: false,
      p_vendors: []
    }
    
    const { data: newSchedule, error: scheduleError } = await supabase
      .rpc('create_schedule_with_vendors', scheduleData)
    
    if (scheduleError) {
      console.log('   ❌ Error creating schedule:', scheduleError)
      return
    }
    
    console.log(`   ✅ Schedule created: ${newSchedule.custom_id} (ID: ${newSchedule.id})`)
    
    // Test 4: Try SharePoint folder creation manually with detailed error logging
    console.log('\n4️⃣ Testing SharePoint folder creation manually...')
    
    try {
      // Try to simulate the exact same import and call as in the API
      console.log('   🔄 Attempting dynamic import...')
      
      // Use require instead of dynamic import for better error handling
      const sharePointServicePath = require('path').join(__dirname, 'src/lib/sharepoint-service.ts')
      
      // Since we can't directly require TypeScript, let's try calling the API instead
      console.log('   🔄 Calling SharePoint API endpoint...')
      
      const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const result = await response.json()
        console.log('   ✅ SharePoint API call successful')
        console.log('   📊 Result:', JSON.stringify(result, null, 2))
      } else {
        const errorText = await response.text()
        console.log('   ❌ SharePoint API call failed:', response.status, errorText)
      }
      
    } catch (sharePointError) {
      console.log('   ❌ SharePoint folder creation error:', sharePointError.message)
      console.log('   📋 Full error:', sharePointError)
    }
    
    // Test 5: Check if folder was created after API call
    console.log('\n5️⃣ Checking if SharePoint folder was created...')
    
    const { data: finalCheck } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', newSchedule.id)
      .single()
    
    if (finalCheck && finalCheck.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder created!')
      console.log(`   📁 Folder ID: ${finalCheck.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${finalCheck.sharepoint_folder_url}`)
    } else {
      console.log('   ❌ SharePoint folder not created')
    }
    
    console.log('\n🎯 Test Complete!')
    console.log(`\n💡 You can clean up by deleting schedule ${newSchedule.custom_id} if needed.`)
    
  } catch (error) {
    console.error('❌ Test error:', error)
  }
}

testSharePointError()