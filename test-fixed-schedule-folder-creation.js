import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testFixedScheduleFolderCreation() {
  console.log('🧪 Testing FIXED schedule folder creation (main folder only)...\n')

  try {
    // 1. Find a valid project and pilot for testing
    console.log('🔍 Step 1: Finding valid project and pilot...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select(`
        id, 
        name, 
        client_id,
        clients!inner(id, name, custom_id)
      `)
      .limit(1)
    
    const { data: pilots } = await supabase
      .from('users')
      .select('id, name')
      .eq('role', 'pilot')
      .limit(1)
    
    if (!projects || projects.length === 0) {
      console.log('   ❌ No projects found - cannot test schedule creation')
      return
    }
    
    if (!pilots || pilots.length === 0) {
      console.log('   ❌ No pilots found - cannot test schedule creation')
      return
    }

    const project = projects[0]
    const pilot = pilots[0]
    
    console.log(`   ✅ Using project: ${project.name} (${project.id})`)
    console.log(`   ✅ Using pilot: ${pilot.name} (${pilot.id})`)

    // 2. Create a test schedule
    console.log('\n🔍 Step 2: Creating test schedule...')
    
    const scheduleData = {
      project_id: project.id,
      scheduled_date: '2025-01-15',
      scheduled_end_date: '2025-01-15',
      status: 'scheduled',
      pilot_id: pilot.id,
      amount: 5000,
      location: 'Test Location',
      notes: 'Test schedule for folder creation verification',
      is_recurring: false,
      is_outsourced: false
    }

    const { data: schedule, error: createError } = await supabase
      .from('schedules')
      .insert(scheduleData)
      .select(`
        id, 
        custom_id, 
        project_id, 
        scheduled_date, 
        pilot_id, 
        amount, 
        location, 
        notes,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link
      `)
      .single()

    if (createError) {
      throw new Error(`Failed to create schedule: ${createError.message}`)
    }

    console.log(`   ✅ Schedule created: ${schedule.custom_id} (${schedule.id})`)
    console.log(`   📅 Date: ${schedule.scheduled_date}`)
    console.log(`   📍 Location: ${schedule.location}`)

    // 3. Trigger SharePoint folder creation via API
    console.log('\n🔍 Step 3: Triggering SharePoint folder creation...')
    
    try {
      const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scheduleId: schedule.id
        })
      })
      
      if (response.ok) {
        const result = await response.json()
        console.log('   ✅ SharePoint folder creation triggered successfully')
        console.log(`   📋 Result: ${result.message}`)
      } else {
        console.log(`   ❌ SharePoint folder creation failed: ${response.status}`)
        const errorText = await response.text()
        console.log(`   Error: ${errorText}`)
      }
    } catch (apiError) {
      console.log('   ❌ SharePoint API connection failed:', apiError.message)
    }

    // 4. Wait a bit for the background job to process
    console.log('\n⏳ Step 4: Waiting for background job to process...')
    await new Promise(resolve => setTimeout(resolve, 5000))

    // 5. Check the database to verify folder information
    console.log('\n🔍 Step 5: Verifying database storage...')
    
    const { data: updatedSchedule, error: fetchError } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link,
        project:projects(
          name,
          clients!inner(name, custom_id)
        )
      `)
      .eq('id', schedule.id)
      .single()

    if (fetchError) {
      throw new Error(`Failed to fetch updated schedule: ${fetchError.message}`)
    }

    console.log('\n📊 DATABASE VERIFICATION RESULTS:')
    console.log('=====================================')
    
    const hasMainFolder = !!updatedSchedule.sharepoint_folder_id
    const hasMainUrl = !!updatedSchedule.sharepoint_folder_url
    const hasShareLink = !!updatedSchedule.sharepoint_share_link
    
    console.log(`✅ Main Schedule Folder ID: ${hasMainFolder ? 'PRESENT' : 'MISSING'}`)
    if (hasMainFolder) {
      console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
    }
    
    console.log(`✅ Main Schedule Folder URL: ${hasMainUrl ? 'PRESENT' : 'MISSING'}`)
    if (hasMainUrl) {
      console.log(`   🌐 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
    }
    
    console.log(`✅ Main Schedule Share Link: ${hasShareLink ? 'PRESENT' : 'MISSING'}`)
    if (hasShareLink) {
      console.log(`   🔗 Share Link: ${updatedSchedule.sharepoint_share_link}`)
    }

    // 6. Verify that Raw/Output subfolder columns are NOT present (this is the fix)
    console.log('\n🔍 Step 6: Verifying NO Raw/Output subfolder storage...')
    
    // Check if the old columns exist in the database schema
    const { data: schemaInfo } = await supabase
      .rpc('get_table_columns', { table_name: 'schedules' })
      .catch(() => null) // Ignore if RPC doesn't exist
    
    if (schemaInfo) {
      const hasRawColumns = schemaInfo.some((col) => 
        col.column_name === 'sharepoint_raw_folder_id' || 
        col.column_name === 'sharepoint_output_folder_id'
      )
      
      if (hasRawColumns) {
        console.log('   ⚠️  WARNING: Old Raw/Output columns still exist in schema')
      } else {
        console.log('   ✅ CONFIRMED: Old Raw/Output columns have been removed from schema')
      }
    } else {
      console.log('   ℹ️  Could not verify schema (RPC not available)')
    }

    // 7. Summary
    console.log('\n🎯 SUMMARY:')
    console.log('============')
    
    if (hasMainFolder && hasMainUrl) {
      console.log('✅ SUCCESS: Main schedule folder is properly saved to database')
      console.log('✅ SUCCESS: Raw/Output subfolders are NOT saved to database (as intended)')
      console.log('✅ SUCCESS: Only the main folder link is stored')
      console.log('\n🎉 BUG FIXED: Schedule folder creation now works correctly!')
    } else {
      console.log('❌ ISSUE: Main schedule folder information is missing from database')
      console.log('❌ This indicates the folder creation process may have failed')
    }

    console.log('\n📝 Expected Behavior:')
    console.log('   - ✅ Main schedule folder: SAVED to database')
    console.log('   - ✅ Raw subfolder: CREATED in SharePoint but NOT saved to database')
    console.log('   - ✅ Output subfolder: CREATED in SharePoint but NOT saved to database')
    console.log('   - ✅ Only main folder ID, URL, and share link are stored')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testFixedScheduleFolderCreation()
  .then(() => {
    console.log('\n🏁 Test completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Test failed:', error)
    process.exit(1)
  })
