import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)

async function createTestData() {
  console.log('🔧 Creating test data...\n')
  
  try {
    // 1. Create a test client
    console.log('👥 Step 1: Creating test client...')
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .insert({
        custom_id: 'CYM-001',
        name: 'Test Client',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Test Street, Test City',
        has_gst: false,
        client_type: 'corporate'
      })
      .select('id, custom_id, name')
      .single()
    
    if (clientError) {
      throw new Error(`Failed to create client: ${clientError.message}`)
    }
    
    console.log(`   ✅ Client created: ${client.custom_id} - ${client.name}`)
    
    // 2. Create a test pilot
    console.log('\n👨‍✈️ Step 2: Creating test pilot...')
    const { data: pilot, error: pilotError } = await supabase
      .from('users')
      .insert({
        email: '<EMAIL>',
        name: 'Test Pilot',
        role: 'pilot'
      })
      .select('id, name, role')
      .single()
    
    if (pilotError) {
      throw new Error(`Failed to create pilot: ${pilotError.message}`)
    }
    
    console.log(`   ✅ Pilot created: ${pilot.name} (${pilot.role})`)
    
    // 3. Create a test project
    console.log('\n📁 Step 3: Creating test project...')
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        custom_id: 'PRJ-001',
        name: 'Test Project',
        description: 'A test project for folder creation testing',
        client_id: client.id,
        location: 'Test Location',
        status: 'active',
        total_amount: 10000,
        gst_inclusive: true,
        amount_received: 5000,
        amount_pending: 5000
      })
      .select('id, custom_id, name, client_id')
      .single()
    
    if (projectError) {
      throw new Error(`Failed to create project: ${projectError.message}`)
    }
    
    console.log(`   ✅ Project created: ${project.custom_id} - ${project.name}`)
    
    // 4. Create a test schedule
    console.log('\n📅 Step 4: Creating test schedule...')
    const { data: schedule, error: scheduleError } = await supabase
      .from('schedules')
      .insert({
        project_id: project.id,
        scheduled_date: '2025-01-15',
        scheduled_end_date: '2025-01-15',
        status: 'scheduled',
        pilot_id: pilot.id,
        amount: 5000,
        location: 'Test Location',
        notes: 'Test schedule for folder creation verification',
        is_recurring: false,
        is_outsourced: false
      })
      .select('id, custom_id, project_id, scheduled_date, pilot_id, amount, location, notes')
      .single()
    
    if (scheduleError) {
      throw new Error(`Failed to create schedule: ${scheduleError.message}`)
    }
    
    console.log(`   ✅ Schedule created: ${schedule.custom_id}`)
    console.log(`   📅 Date: ${schedule.scheduled_date}`)
    console.log(`   📍 Location: ${schedule.location}`)
    console.log(`   💰 Amount: ${schedule.amount}`)
    
    console.log('\n🎉 Test data created successfully!')
    console.log('\n📊 Summary:')
    console.log(`   - Client: ${client.custom_id} - ${client.name}`)
    console.log(`   - Pilot: ${pilot.name} (${pilot.role})`)
    console.log(`   - Project: ${project.custom_id} - ${project.name}`)
    console.log(`   - Schedule: ${schedule.custom_id} - ${schedule.scheduled_date}`)
    
    console.log('\n🚀 Now you can test the SharePoint folder creation!')
    console.log(`   Schedule ID: ${schedule.id}`)
    console.log(`   Schedule Custom ID: ${schedule.custom_id}`)
    
    return {
      client,
      pilot,
      project,
      schedule
    }
    
  } catch (error) {
    console.error('❌ Failed to create test data:', error.message)
    throw error
  }
}

createTestData()
  .then((data) => {
    console.log('\n🏁 Test data creation completed')
    console.log('\n💡 Next steps:')
    console.log('   1. The schedule should automatically trigger SharePoint folder creation')
    console.log('   2. Check the background jobs: curl http://localhost:3000/api/background-jobs')
    console.log('   3. Wait a few seconds and check the schedule again')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Test data creation failed:', error)
    process.exit(1)
  })
