const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'

console.log('🔗 Connecting to Supabase...')
const supabase = createClient(supabaseUrl, supabaseKey)

async function checkUsers() {
  console.log('👥 Checking users in auth.users...')
  
  const { data: users, error: usersError } = await supabase.auth.admin.listUsers()
  
  if (usersError) {
    console.error('❌ Error fetching users:', usersError)
    return
  }
  
  console.log(`Found ${users.users?.length || 0} users:`)
  if (users.users && users.users.length > 0) {
    users.users.forEach(user => {
      console.log(`- ${user.email} (ID: ${user.id})`)
    })
    return users.users[0].id
  } else {
    console.log('No users found')
    return null
  }
}

checkUsers().catch(console.error)