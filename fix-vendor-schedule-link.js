const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'

console.log('🔗 Connecting to Supabase...')
const supabase = createClient(supabaseUrl, supabaseKey)

async function fixVendorScheduleLink() {
  console.log('🔍 Finding Arun photography vendor...')
  
  // Find Arun photography vendor
  const { data: vendor, error: vendorError } = await supabase
    .from('outsourcing_vendors')
    .select('*')
    .eq('name', 'Arun photography')
    .single()
    
  if (vendorError || !vendor) {
    console.error('❌ Vendor not found:', vendorError)
    return
  }
  
  console.log('✅ Found vendor:', vendor.name, '(ID:', vendor.id + ')')
  
  // Find the latest schedule
  console.log('🔍 Finding latest schedule...')
  const { data: schedule, error: scheduleError } = await supabase
    .from('schedules')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(1)
    .single()
    
  if (scheduleError || !schedule) {
    console.error('❌ Schedule not found:', scheduleError)
    return
  }
  
  console.log('✅ Found schedule:', schedule.id)
  
  // Check if schedule_vendors table exists and link exists
  console.log('🔍 Checking schedule_vendors link...')
  const { data: existingLink, error: linkError } = await supabase
    .from('schedule_vendors')
    .select('*')
    .eq('schedule_id', schedule.id)
    .eq('vendor_id', vendor.id)
    .single()
    
  if (existingLink) {
    console.log('✅ Link already exists:', existingLink)
    return
  }
  
  // Create the link
  console.log('🔗 Creating schedule_vendors link...')
  const { data: newLink, error: newLinkError } = await supabase
    .from('schedule_vendors')
    .insert({
      schedule_id: schedule.id,
      vendor_id: vendor.id,
      cost: 4000,
      notes: 'Photography services'
    })
    .select()
    .single()
    
  if (newLinkError) {
    console.error('❌ Error creating link:', newLinkError)
    return
  }
  
  console.log('✅ Schedule-vendor link created successfully!')
  
  // Test the API logic again
  console.log('\n🧪 Testing vendor-shoots API logic again...')
  
  const { data: scheduleVendors, error: svError } = await supabase
    .from('schedule_vendors')
    .select(`
      schedule_id,
      cost,
      notes,
      schedules!inner (
        id,
        project_id,
        scheduled_date,
        amount,
        location,
        status,
        projects!inner (
          id,
          name,
          client_id,
          vendor_payment_status,
          vendor_payment_amount,
          clients!inner (
            id,
            name
          )
        )
      )
    `)
    .eq('vendor_id', vendor.id)
    
  if (svError) {
    console.error('❌ Error fetching schedule vendors:', svError)
    return
  }
  
  console.log('📋 Schedule Vendors Data:', JSON.stringify(scheduleVendors, null, 2))
  
  // Calculate totals
  let totalPaid = 0
  let totalPending = 0
  
  scheduleVendors.forEach(sv => {
    const project = sv.schedules.projects
    const paymentStatus = project.vendor_payment_status
    const paymentAmount = project.vendor_payment_amount || sv.cost || 0
    
    if (paymentStatus === 'paid') {
      totalPaid += paymentAmount
    } else {
      totalPending += paymentAmount
    }
  })
  
  console.log('\n💰 Payment Summary:')
  console.log('Total Paid: ₹' + totalPaid)
  console.log('Total Pending: ₹' + totalPending)
  console.log('Total Projects:', scheduleVendors.length)
}

fixVendorScheduleLink().catch(console.error)