import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

// Use service role key to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function checkExistingData() {
  console.log('🔍 Checking existing data with service role...\n')
  
  try {
    // Check clients
    const { data: clients, error: clientsError } = await supabase
      .from('clients')
      .select('id, custom_id, name, created_at')
      .order('created_at', { ascending: false })
      .limit(10)
    
    console.log('👥 Clients:')
    if (clientsError) {
      console.log('   ❌ Error:', clientsError.message)
    } else if (!clients || clients.length === 0) {
      console.log('   ❌ No clients found')
    } else {
      console.log(`   ✅ Found ${clients.length} clients:`)
      clients.forEach(c => console.log(`      - ${c.custom_id}: ${c.name} (${c.created_at})`))
    }
    
    // Check projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, custom_id, name, created_at')
      .order('created_at', { ascending: false })
      .limit(10)
    
    console.log('\n📁 Projects:')
    if (projectsError) {
      console.log('   ❌ Error:', projectsError.message)
    } else if (!projects || projects.length === 0) {
      console.log('   ❌ No projects found')
    } else {
      console.log(`   ✅ Found ${projects.length} projects:`)
      projects.forEach(p => console.log(`      - ${p.custom_id}: ${p.name} (${p.created_at})`))
    }
    
    // Check schedules
    const { data: schedules, error: schedulesError } = await supabase
      .from('schedules')
      .select('id, custom_id, project_id, scheduled_date, created_at')
      .order('created_at', { ascending: false })
      .limit(10)
    
    console.log('\n📅 Schedules:')
    if (schedulesError) {
      console.log('   ❌ Error:', schedulesError.message)
    } else if (!schedules || schedules.length === 0) {
      console.log('   ❌ No schedules found')
    } else {
      console.log(`   ✅ Found ${schedules.length} schedules:`)
      schedules.forEach(s => console.log(`      - ${s.custom_id}: ${s.scheduled_date} (${s.created_at})`))
    }
    
    // Check users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, name, role, created_at')
      .order('created_at', { ascending: false })
      .limit(10)
    
    console.log('\n👤 Users:')
    if (usersError) {
      console.log('   ❌ Error:', usersError.message)
    } else if (!users || users.length === 0) {
      console.log('   ❌ No users found')
    } else {
      console.log(`   ✅ Found ${users.length} users:`)
      users.forEach(u => console.log(`      - ${u.name} (${u.role}) (${u.created_at})`))
    }
    
  } catch (error) {
    console.error('❌ Error checking data:', error.message)
  }
}

checkExistingData()
  .then(() => {
    console.log('\n🏁 Check completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Check failed:', error)
    process.exit(1)
  })