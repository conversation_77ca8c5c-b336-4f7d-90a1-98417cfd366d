import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function checkUsersAndRoles() {
  console.log('👥 Checking users and their roles...\n')

  try {
    // Get all users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')

    if (usersError) {
      throw new Error(`Failed to get users: ${usersError.message}`)
    }

    console.log(`Found ${users.length} users:`)
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email}) - Role: ${user.role}`)
    })

    // Check for pilots specifically
    const pilots = users.filter(user => user.role === 'pilot')
    console.log(`\n🚁 Pilots found: ${pilots.length}`)

    if (pilots.length === 0) {
      console.log('❌ No pilots found. Available roles:', [...new Set(users.map(u => u.role))])
      
      // Let's use any user for testing
      if (users.length > 0) {
        console.log(`\n💡 Using first available user for testing: ${users[0].name} (${users[0].role})`)
        return users[0]
      }
    } else {
      console.log(`✅ Using pilot: ${pilots[0].name}`)
      return pilots[0]
    }

  } catch (error) {
    console.error('❌ Check failed:', error.message)
    process.exit(1)
  }
}

checkUsersAndRoles()