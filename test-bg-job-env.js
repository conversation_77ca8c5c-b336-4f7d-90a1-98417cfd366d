#!/usr/bin/env node

// Test environment variables in background job context
console.log('🧪 Testing environment variables in background job context...\n')

// Load environment variables like Next.js does
require('dotenv').config({ path: '.env.local' })

console.log('Environment variables check:')
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Present' : 'Missing')
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Present' : 'Missing')
console.log('MICROSOFT_CLIENT_ID:', process.env.MICROSOFT_CLIENT_ID ? 'Present' : 'Missing')
console.log('MICROSOFT_CLIENT_SECRET:', process.env.MICROSOFT_CLIENT_SECRET ? 'Present' : 'Missing')
console.log('MICROSOFT_TENANT_ID:', process.env.MICROSOFT_TENANT_ID ? 'Present' : 'Missing')

console.log('\nActual values (first 10 chars):')
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 10) + '...')
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 10) + '...')
console.log('MICROSOFT_CLIENT_ID:', process.env.MICROSOFT_CLIENT_ID?.substring(0, 10) + '...')

// Test the exact same validation logic as in the microsoft-graph.ts file
console.log('\n🔍 Testing validation logic from microsoft-graph.ts:')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('supabaseUrl check:', supabaseUrl ? 'PASS' : 'FAIL')
console.log('supabaseServiceKey check:', supabaseServiceKey ? 'PASS' : 'FAIL')

if (!supabaseUrl) {
  console.log('❌ NEXT_PUBLIC_SUPABASE_URL is required')
}

if (!supabaseServiceKey) {
  console.log('❌ SUPABASE_SERVICE_ROLE_KEY is required')
}

if (supabaseUrl && supabaseServiceKey) {
  console.log('✅ All environment variables are present')
}