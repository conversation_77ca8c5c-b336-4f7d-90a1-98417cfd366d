-- Migration: Update expense categories
-- Date: 2025-01-23
-- Description: Updates expense categories to match the new business requirements

-- First, let's see what existing categories we have
-- SELECT DISTINCT category FROM expenses;

-- Update existing expense categories to map to new categories
-- Map old categories to new ones where possible
UPDATE expenses SET category = 'fuel_travel' WHERE category = 'fuel';
UPDATE expenses SET category = 'gadgets' WHERE category = 'equipment';
UPDATE expenses SET category = 'fuel_travel' WHERE category = 'travel';
UPDATE expenses SET category = 'others' WHERE category = 'maintenance';
UPDATE expenses SET category = 'others' WHERE category = 'other';

-- Add a comment to document the change
COMMENT ON COLUMN expenses.category IS 'Expense category: cymatics, salary, gadgets, outsourcing, asset, loan_repayment, investments, fuel_travel, food_snacks, others, entertainment, gopi, yaso, adithyan';

-- Note: You may need to create a check constraint to enforce the new categories
-- ALTER TABLE expenses ADD CONSTRAINT check_expense_category 
-- CHECK (category IN ('cymatics', 'salary', 'gadgets', 'outsourcing', 'asset', 'loan_repayment', 'investments', 'fuel_travel', 'food_snacks', 'others', 'entertainment', 'gopi', 'yaso', 'adithyan'));

-- Update the updated_at timestamp for any existing records
UPDATE expenses SET updated_at = NOW() WHERE updated_at IS NOT NULL;
