-- Migration: Add client type field to clients table
-- Date: 2025-01-23
-- Description: Adds an optional client_type field to the clients table to categorize clients

-- Add the client_type column to the clients table
ALTER TABLE clients 
ADD COLUMN client_type TEXT;

-- Add a comment to document the column
COMMENT ON COLUMN clients.client_type IS 'Type/category of the client (Wedding, Corporate, Movie, Govt, NGO, Survey, Surveillance, Real estate, News, Event, Collaboration)';

-- Create an index for faster searches on client type (optional but recommended)
CREATE INDEX idx_clients_client_type ON clients(client_type) WHERE client_type IS NOT NULL;

-- Update the updated_at timestamp for any existing records (optional)
-- This ensures the updated_at field reflects the schema change
UPDATE clients SET updated_at = NOW() WHERE updated_at IS NOT NULL;
