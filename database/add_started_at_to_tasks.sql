-- Migration: Add started_at field to tasks table
-- Date: 2025-08-04
-- Description: Adds started_at timestamp field to track when tasks are started

-- Add started_at column to tasks table
ALTER TABLE tasks ADD COLUMN started_at TIMESTAMP WITH TIME ZONE;

-- Add comment to document the field
COMMENT ON COLUMN tasks.started_at IS 'Timestamp when the task was started (status changed to in_progress)';

-- Update the updated_at timestamp for any existing records
UPDATE tasks SET updated_at = NOW() WHERE updated_at IS NOT NULL;
