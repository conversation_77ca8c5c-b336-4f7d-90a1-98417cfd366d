-- Migration: Ren<PERSON> shoots table to schedules
-- Date: 2025-08-04
-- Description: Renames the shoots table to schedules to avoid naming conflicts

-- Rename the table
ALTER TABLE shoots RENAME TO schedules;

-- Update foreign key references in tasks table
-- The column name shoot_id in tasks will remain as is since it refers to a specific shoot within a schedule

-- Update any indexes that reference the old table name
-- Note: PostgreSQL automatically updates index names when table is renamed

-- Update any triggers that reference the old table name
-- Note: PostgreSQL automatically updates trigger references when table is renamed

-- Add comment to document the change
COMMENT ON TABLE schedules IS 'Renamed from shoots table. Contains scheduled drone sessions/shoots for projects';

-- Update the updated_at timestamp for any existing records
UPDATE schedules SET updated_at = NOW() WHERE updated_at IS NOT NULL;
