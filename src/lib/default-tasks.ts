import type { CreateTaskForm } from '@/types'

export interface DefaultTaskTemplate {
  title: string
  description?: string
  assigned_role: 'pilot' | 'editor' | 'accounts'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  order: number
  dueDaysAfterShoot?: number // Days after shoot date
  dueDaysAfterTask?: string // Task title to calculate due date from
  isProjectTask?: boolean // true for project-level tasks, false for shoot-based tasks
}

// Default task templates for each client type
export const DEFAULT_TASK_TEMPLATES: Record<string, DefaultTaskTemplate[]> = {
  // Wedding, Movie, Surveillance, Events, News, Collaboration
  Wedding: [
    { title: 'Shoot', description: 'Conduct the wedding shoot', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 2, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],
  Movie: [
    { title: 'Shoot', description: 'Conduct the movie shoot', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 2, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],
  Surveillance: [
    { title: 'Shoot', description: 'Conduct the surveillance shoot', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 2, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],
  Event: [
    { title: 'Shoot', description: 'Conduct the event shoot', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 2, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],
  News: [
    { title: 'Shoot', description: 'Conduct the news shoot', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 2, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],
  Collaboration: [
    { title: 'Shoot', description: 'Conduct the collaboration shoot', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 2, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],

  // Corporate, Real Estate
  Corporate: [
    { title: 'Script Confirmation', description: 'Confirm script and requirements with client', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'Shoot', description: 'Conduct the corporate shoot', assigned_role: 'pilot', priority: 'high', order: 2, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 4, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Edit', description: 'Edit and post-process the footage', assigned_role: 'editor', priority: 'high', order: 5, dueDaysAfterShoot: 7, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],
  'Real estate': [
    { title: 'Script Confirmation', description: 'Confirm property details and requirements', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'Shoot', description: 'Conduct the real estate shoot', assigned_role: 'pilot', priority: 'high', order: 2, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 4, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Edit', description: 'Edit and enhance the property footage', assigned_role: 'editor', priority: 'high', order: 5, dueDaysAfterShoot: 7, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],

  // Government, NGO
  Govt: [
    { title: 'Shoot', description: 'Conduct the government project shoot', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 2, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Edit', description: 'Edit and process the footage', assigned_role: 'editor', priority: 'medium', order: 4, dueDaysAfterShoot: 7, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],
  NGO: [
    { title: 'Shoot', description: 'Conduct the NGO project shoot', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured files to storage', assigned_role: 'pilot', priority: 'medium', order: 2, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all files', assigned_role: 'pilot', priority: 'medium', order: 3, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Edit', description: 'Edit and process the footage', assigned_role: 'editor', priority: 'medium', order: 4, dueDaysAfterShoot: 7, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver final files to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],

  // Survey
  Survey: [
    { title: 'Plan Flight', description: 'Plan the survey flight path and parameters', assigned_role: 'pilot', priority: 'high', order: 1, isProjectTask: false },
    { title: 'Mark GCPs', description: 'Mark Ground Control Points for survey accuracy', assigned_role: 'pilot', priority: 'high', order: 2, isProjectTask: false },
    { title: 'Shoot', description: 'Conduct the survey shoot', assigned_role: 'pilot', priority: 'high', order: 3, isProjectTask: false },
    { title: 'File Upload', description: 'Upload captured survey data to storage', assigned_role: 'pilot', priority: 'medium', order: 4, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'File Backup', description: 'Create backup of all survey data', assigned_role: 'pilot', priority: 'medium', order: 5, dueDaysAfterShoot: 1, isProjectTask: false },
    { title: 'Post-Processing', description: 'Process survey data and generate maps/models', assigned_role: 'pilot', priority: 'high', order: 6, dueDaysAfterShoot: 7, isProjectTask: false },
    { title: 'Deliver Files', description: 'Deliver processed survey results to client', assigned_role: 'accounts', priority: 'medium', order: 100, dueDaysAfterShoot: 7, isProjectTask: true },
    { title: 'Payment Collect', description: 'Collect payment from client', assigned_role: 'accounts', priority: 'high', order: 101, dueDaysAfterTask: 'Deliver Files', dueDaysAfterShoot: 7, isProjectTask: true },
  ],
}

/**
 * Get default tasks for a specific client type
 */
export function getDefaultTasksForClientType(clientType: string): DefaultTaskTemplate[] {
  return DEFAULT_TASK_TEMPLATES[clientType] || []
}

/**
 * Create task forms from templates for a specific project
 */
export function createTasksFromTemplates(
  templates: DefaultTaskTemplate[],
  projectId: string,
  usersByRole: Record<string, string>, // Map of role to user ID
  shootDate?: string, // Optional shoot date for due date calculation
  shootId?: string // Optional shoot ID for shoot-based tasks
): CreateTaskForm[] {
  const sortedTemplates = templates.sort((a, b) => a.order - b.order)

  return sortedTemplates.map((template) => {
    let dueDate: string | undefined

    // Special case: Shoot task should have due date as the schedule start time
    if (shootDate && template.title === 'Shoot') {
      dueDate = shootDate // Use the full schedule date/time
    }
    // Calculate due date if shoot date is provided
    else if (shootDate && template.dueDaysAfterShoot) {
      const shootDateTime = new Date(shootDate)
      const dueDateObj = new Date(shootDateTime)
      dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot)
      dueDate = dueDateObj.toISOString().split('T')[0] // Format as YYYY-MM-DD
    }

    // Handle tasks that depend on other tasks (like Payment Collect after Deliver Files)
    if (shootDate && template.dueDaysAfterTask && template.dueDaysAfterShoot) {
      const shootDateTime = new Date(shootDate)
      const dueDateObj = new Date(shootDateTime)
      dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot)
      dueDate = dueDateObj.toISOString().split('T')[0]
    }

    return {
      title: template.title,
      description: template.description,
      status: 'pending' as const,
      priority: template.priority,
      assigned_to: usersByRole[template.assigned_role] || '', // Empty string will be filtered out later
      assigned_role: template.assigned_role, // Add the assigned role field
      project_id: projectId,
      shoot_id: template.isProjectTask ? undefined : shootId, // Assign to shoot only if it's a shoot-based task
      due_date: dueDate,
      order: template.order, // Add the order field
    }
  })
}

/**
 * Get all available client types that have default tasks
 */
export function getClientTypesWithDefaultTasks(): string[] {
  return Object.keys(DEFAULT_TASK_TEMPLATES)
}
