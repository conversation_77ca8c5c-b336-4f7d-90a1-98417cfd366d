'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Modal } from '@/components/ui/modal'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { ProjectForm } from '@/components/forms/ProjectForm'
import { ScheduleForm } from '@/components/forms/ScheduleForm'
import { PaymentForm } from '@/components/forms/PaymentForm'
import { TaskForm } from '@/components/forms/TaskForm'
import { VendorPaymentForm } from '@/components/forms/VendorPaymentForm'
import { ShootCompletionForm, type ShootCompletionData } from '@/components/ui/schedule-completion-form'
import { AddExpenseForm } from '@/components/forms/AddExpenseForm'
import { ExpenseForm } from '@/components/forms/ExpenseForm'
import { ExpenseCard } from '@/components/ui/expense-card'
import { VendorPaymentCard } from '@/components/ui/vendor-payment-card'
import { RecordVendorPaymentForm } from '@/components/forms/RecordVendorPaymentForm'
import { EnhancedTaskCard } from '@/components/ui/enhanced-task-card'
import { RedesignedTaskCard } from '@/components/ui/redesigned-task-card'
import { ScheduleCard } from '@/components/ui/schedule-card'
import { projectsApi, paymentsApi, shootsApi, tasksApi, vendorsApi, expensesApi, usersApi } from '@/lib/api'
import {
  AlertCircle,
  ArrowLeft,
  ArrowUpDown,
  Building,
  Building2,
  Calendar,
  Camera,
  CheckCircle,
  Clock,
  DollarSign,
  Edit,
  ExternalLink,
  Filter,
  MapPin,
  MoreVertical,
  Plus,
  Receipt,
  TrendingUp,
  User,
  X
} from 'lucide-react'
import toast from 'react-hot-toast'
import type { Project, Payment, Shoot, Task, OutsourcingVendor, Expense } from '@/types'
import Link from 'next/link'
import { formatGoogleMapsUrl } from '@/lib/google-maps-utils'
import { formatDate } from '@/lib/utils'
import { useProjectExpenses } from '@/hooks/useProjectExpenses'
import { calculateProjectTotal, calculateOutsourcingExpenses } from '@/lib/project-calculations'
import { useAuth } from '@/contexts/AuthContext'

export default function ProjectDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const projectId = params.id as string

  const [project, setProject] = useState<Project | null>(null)
  const [payments, setPayments] = useState<Payment[]>([])
  const [schedules, setSchedules] = useState<Shoot[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [vendors, setVendors] = useState<OutsourcingVendor[]>([])
  const [users, setUsers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isAddShootModalOpen, setIsAddShootModalOpen] = useState(false)
  const [editingShoot, setEditingShoot] = useState<Shoot | null>(null)
  const [completingShoot, setCompletingShoot] = useState<Shoot | null>(null)
  const [isAddPaymentModalOpen, setIsAddPaymentModalOpen] = useState(false)
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false)
  const [isVendorPaymentModalOpen, setIsVendorPaymentModalOpen] = useState(false)
  const [isAddExpenseModalOpen, setIsAddExpenseModalOpen] = useState(false)
  const [isEditExpenseModalOpen, setIsEditExpenseModalOpen] = useState(false)
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null)
  const [isRecordVendorPaymentModalOpen, setIsRecordVendorPaymentModalOpen] = useState(false)
  const [selectedVendor, setSelectedVendor] = useState<OutsourcingVendor | null>(null)
  const [shootSortBy, setShootSortBy] = useState<'date' | 'status' | 'amount'>('date')
  const [shootSortOrder, setShootSortOrder] = useState<'asc' | 'desc'>('desc')
  const [shootStatusFilter, setShootStatusFilter] = useState<string>('all')
  const { expenses, loading: expensesLoading, error: expensesError, refetch: refetchExpenses } = useProjectExpenses(projectId)
  const outsourcingExpenses = expenses ? calculateOutsourcingExpenses(expenses) : 0
  const totalExpenses = expenses?.reduce((sum, expense) => {
    // Exclude outsourcing expenses from the main total
    // Ensure expense and category are defined before comparison
    if (expense && expense.category && expense.category.toLowerCase() === 'outsourcing') {
      return sum
    }
    return sum + (expense?.amount || 0)
  }, 0) || 0
  const calculation = project && project.client ?
   calculateProjectTotal(schedules || [], project.client, totalExpenses, outsourcingExpenses) :
    { profit: 0, outsourcing: 0, subtotal: 0, gstAmount: 0, total: 0, hasGst: false, expenses: 0 }
  const { profit, outsourcing: totalOutsourcing } = calculation

  const fetchProjectData = async () => {
    try {
      setLoading(true)
      // Diagnose which call fails by splitting and logging individually
      console.warn('[ProjectDetails] fetchProjectData start', { projectId })

      let projectData: Project | null = null
      let paymentsData: Payment[] = []
      let schedulesData: Shoot[] = []
      let tasksData: Task[] = []
      let usersData: any[] = []

      try {
        projectData = await projectsApi.getById(projectId)
        console.warn('[ProjectDetails] projectsApi.getById OK', { hasProject: !!projectData })
      } catch (e: any) {
        console.warn('[ProjectDetails] projectsApi.getById FAILED', { message: e?.message, code: e?.code, details: e })
        throw e
      }

      try {
        paymentsData = await paymentsApi.getAll()
        console.warn('[ProjectDetails] paymentsApi.getAll OK', { count: paymentsData?.length })
      } catch (e: any) {
        console.warn('[ProjectDetails] paymentsApi.getAll FAILED', { message: e?.message, code: e?.code, details: e })
        throw e
      }

      try {
        schedulesData = await shootsApi.getAll()
        console.warn('[ProjectDetails] shootsApi.getAll OK', { count: schedulesData?.length })
      } catch (e: any) {
        console.warn('[ProjectDetails] shootsApi.getAll FAILED', { message: e?.message, code: e?.code, details: e })
        throw e
      }

      try {
        tasksData = await tasksApi.getAll()
        console.warn('[ProjectDetails] tasksApi.getAll OK', { count: tasksData?.length })
      } catch (e: any) {
        console.warn('[ProjectDetails] tasksApi.getAll FAILED', { message: e?.message, code: e?.code, details: e })
        throw e
      }

      try {
        usersData = await usersApi.getAll()
        console.warn('[ProjectDetails] usersApi.getAll OK', { count: usersData?.length })
      } catch (e: any) {
        console.warn('[ProjectDetails] usersApi.getAll FAILED', { message: e?.message, code: e?.code, details: e })
        throw e
      }

      setProject(projectData)
      setUsers(usersData || [])

      // Filter data for this project
      const projectPayments = paymentsData.filter(payment => payment.project_id === projectId)
      const projectSchedules = schedulesData.filter(schedule => schedule.project_id === projectId)
      const projectTasks = tasksData
        .filter(task => task.project_id === projectId)
        .sort((a, b) => {
          // Enhanced sorting: shoot tasks first, then project tasks
          const aIsProjectTask = !a.shoot_id
          const bIsProjectTask = !b.shoot_id

          // If one is project task and other is shoot task, shoot task comes first
          if (aIsProjectTask && !bIsProjectTask) return 1
          if (!aIsProjectTask && bIsProjectTask) return -1

          // Both are same type, sort by order field
          const aOrder = a.order ?? (aIsProjectTask ? 1000 : 0)
          const bOrder = b.order ?? (bIsProjectTask ? 1000 : 0)

          if (aOrder !== bOrder) {
            return aOrder - bOrder
          }

          // Fallback to creation time
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        })

      // Get unique vendors from project shoots (both legacy single vendor and new multiple vendors)
      const projectVendorIds = [...new Set([
        ...projectSchedules
          .filter(schedule => schedule.is_outsourced && (schedule as any).vendor_id)
          .map(schedule => (schedule as any).vendor_id),
        ...projectSchedules
          .filter(schedule => schedule.is_outsourced && (schedule as any).vendors && (schedule as any).vendors.length > 0)
          .flatMap(schedule => (schedule as any).vendors!.map((sv: any) => sv.vendor_id))
      ])]

      // Fetch vendor details for project vendors
      let projectVendors: OutsourcingVendor[] = []
      if (projectVendorIds.length > 0) {
        try {
          const vendorsData = await vendorsApi.getAll()
          projectVendors = vendorsData.filter(vendor => projectVendorIds.includes(vendor.id))
          console.warn('[ProjectDetails] vendorsApi.getAll OK (filtered)', { total: vendorsData.length, used: projectVendors.length })
        } catch (e: any) {
          console.warn('[ProjectDetails] vendorsApi.getAll FAILED', { message: e?.message, code: e?.code, details: e })
          // Do not throw; vendors list is non-critical
        }
      }

      setPayments(projectPayments)
      setSchedules(projectSchedules)
      setTasks(projectTasks)
      setVendors(projectVendors)
      console.warn('[ProjectDetails] fetchProjectData success', {
        projectPayments: projectPayments.length,
        projectSchedules: projectSchedules.length,
        projectTasks: projectTasks.length,
        projectVendors: projectVendors.length
      })
    } catch (error: any) {
      console.warn('[ProjectDetails] fetchProjectData overall FAILED', { message: error?.message, code: error?.code, details: error })
      toast.error('Failed to load project details')
      router.push('/projects')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (projectId) {
      fetchProjectData()
    }
  }, [projectId])

  const handleEditSuccess = () => {
    setIsEditModalOpen(false)
    fetchProjectData()
  }

  const handleAddScheduleSuccess = () => {
    setIsAddShootModalOpen(false)
    fetchProjectData()
  }

  const handleAddPaymentSuccess = async () => {
    setIsAddPaymentModalOpen(false)
    // Force update project amounts immediately after payment
    try {
      await paymentsApi.updateProjectAmountReceived(projectId)
      await fetchProjectData()
    } catch (error) {
      console.error('Error updating project after payment:', error)
      toast.error('Failed to update project data')
    }
  }



  const handleAddTaskSuccess = () => {
    setIsAddTaskModalOpen(false)
    fetchProjectData()
  }

  const handleRecordVendorPayment = (vendor: OutsourcingVendor) => {
    setSelectedVendor(vendor)
    setIsRecordVendorPaymentModalOpen(true)
  }

  const handleVendorPaymentSuccess = (expense: Expense) => {
    // Refresh project data and expenses to update calculations
    fetchProjectData()
    refetchExpenses()
    setIsRecordVendorPaymentModalOpen(false)
    setSelectedVendor(null)
    toast.success('Vendor payment recorded as expense successfully')
  }

  // Calculate vendor payment data
  const getVendorPaymentData = (vendorId: string) => {
    if (!vendorId || !schedules || !expenses || !vendors) {
      return { totalOwed: 0, totalPaid: 0 }
    }

    const vendorSchedules = schedules.filter(schedule =>
      ((schedule as any)?.vendor_id === vendorId && schedule?.is_outsourced) ||
      (schedule?.is_outsourced && (schedule as any)?.vendors?.some((sv: any) => sv.vendor_id === vendorId))
    )
    const totalOwed = vendorSchedules.reduce((sum, schedule) => {
      // For legacy single vendor schedules
      if ((schedule as any)?.vendor_id === vendorId) {
        return sum + ((schedule as any)?.outsourcing_cost || 0)
      }
      // For multiple vendor schedules
      const vendorEntry = (schedule as any)?.vendors?.find((sv: any) => sv.vendor_id === vendorId)
      return sum + (vendorEntry?.cost || 0)
    }, 0)

    // Calculate actual payments made to this vendor based on expense records
    // We look for expenses with category 'outsourcing' that mention the vendor
    const vendor = vendors.find(v => v?.id === vendorId)
    const vendorName = vendor?.name || ''

    if (!vendorName) {
      return { totalOwed, totalPaid: 0 }
    }

    const vendorPayments = expenses.filter(expense =>
      expense?.category?.toLowerCase() === 'outsourcing' &&
      expense?.description?.toLowerCase().includes(vendorName.toLowerCase())
    )
    const totalPaid = vendorPayments.reduce((sum, expense) =>
      sum + (expense?.amount || 0), 0
    )

    return { totalOwed, totalPaid }
  }

  const handleEditExpense = (expense: Expense) => {
    setEditingExpense(expense)
    setIsEditExpenseModalOpen(true)
  }

  const handleDeleteExpense = async (expense: Expense) => {
    if (!confirm(`Are you sure you want to delete this expense: ${expense.description}?`)) {
      return
    }

    try {
      await expensesApi.delete(expense.id)
      refetchExpenses()
      toast.success('Expense deleted successfully')
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete expense')
    }
  }

  const handleEditExpenseSuccess = () => {
    setIsEditExpenseModalOpen(false)
    setEditingExpense(null)
    refetchExpenses()
  }

  const handleTaskStatusChange = async (task: Task, newStatus: Task['status']) => {
    try {
      // Optimistic update - update local state immediately
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === task.id
            ? { ...t, status: newStatus, updated_at: new Date().toISOString() }
            : t
        )
      )

      await tasksApi.update(task.id, { status: newStatus })
      toast.success('Task status updated successfully')
    } catch (error: any) {
      // Revert optimistic update on error
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === task.id
            ? { ...t, status: task.status }
            : t
        )
      )
      toast.error(error.message || 'Failed to update task status')
    }
  }

  const handleTaskInlineUpdate = async (taskId: string, updates: Partial<Task>) => {
    try {
      // Optimistic update - update local state immediately
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === taskId
            ? { ...t, ...updates, updated_at: new Date().toISOString() }
            : t
        )
      )

      await tasksApi.update(taskId, updates)
      toast.success('Task updated successfully')
    } catch (error: any) {
      // Revert optimistic update on error by refetching
      fetchProjectData()
      toast.error(error.message || 'Failed to update task')
    }
  }

  const handleEditTask = (task: Task) => {
    // You can implement a full edit modal here if needed
    // For now, we'll use inline editing
    console.log('Edit task:', task)
  }

  const handleDeleteTask = async (task: Task) => {
    try {
      // Optimistic update - remove from local state immediately
      setTasks(prevTasks => prevTasks.filter(t => t.id !== task.id))

      await tasksApi.delete(task.id)
      toast.success('Task deleted successfully')
    } catch (error: any) {
      // Revert optimistic update on error by refetching
      fetchProjectData()
      toast.error(error.message || 'Failed to delete task')
    }
  }

  const handleEditShootSuccess = () => {
    setEditingShoot(null)
    fetchProjectData()
  }



  const handleShootStatusChange = async (shoot: Shoot, newStatus: string) => {
    if (newStatus === 'completed') {
      // Open completion form instead of directly marking as complete
      setCompletingShoot(shoot)
      return
    }

    try {
      const updates: any = { status: newStatus }

      await shootsApi.update(shoot.id, updates)
      toast.success(`Shoot marked as ${newStatus}`)
      fetchProjectData()
    } catch (error: any) {
      toast.error(error.message || 'Failed to update shoot status')
    }
  }



  const handleCancelShoot = async (shoot: Shoot) => {
    if (!confirm(`Are you sure you want to cancel this shoot scheduled for ${new Date(shoot.scheduled_date).toLocaleDateString()}? This action can be undone by editing the shoot later.`)) {
      return
    }

    try {
      // Use the same approach as handleShootStatusChange
      await handleShootStatusChange(shoot, 'cancelled')
    } catch (error: any) {
      toast.error(error.message || 'Failed to cancel shoot')
    }
  }

  const handleCompleteShoot = async (completionData: ShootCompletionData) => {
    if (!completingShoot) return

    try {
      const updates = {
        status: 'completed',
        actual_date: new Date().toISOString(),
        device_used: completionData.device_used,
        battery_count: completionData.battery_count,
        shoot_start_time: completionData.shoot_start_time,
        shoot_end_time: completionData.shoot_end_time,
        completion_notes: completionData.completion_notes
      }

      // Complete the shoot first
      await shootsApi.update(completingShoot.id, updates as any)

      // Then complete all shoot-related tasks
      await projectsApi.completeShootTasks(completingShoot.id)

      toast.success('Shoot and all related tasks completed successfully')
      setCompletingShoot(null)
      fetchProjectData()
    } catch (error: any) {
      toast.error(error.message || 'Failed to complete shoot')
      throw error
    }
  }

  const handleAssignmentChange = async (taskId: string, userId: string) => {
    try {
      // Optimistic update - update local state immediately
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === taskId
            ? { ...t, assigned_to: userId, updated_at: new Date().toISOString() }
            : t
        )
      )

      await tasksApi.updateAssignment(taskId, userId)
      toast.success('Task assignment updated successfully')
    } catch (error: any) {
      // Revert optimistic update on error by refetching
      fetchProjectData()
      toast.error(error.message || 'Failed to update task assignment')
    }
  }

  const handleDueDateChange = async (taskId: string, dueDate: string | null) => {
    try {
      // Optimistic update - update local state immediately
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === taskId
            ? { ...t, due_date: dueDate || undefined, updated_at: new Date().toISOString() }
            : t
        )
      )

      await tasksApi.updateDueDate(taskId, dueDate)
      toast.success('Task due date updated successfully')
    } catch (error: any) {
      // Revert optimistic update on error by refetching
      fetchProjectData()
      toast.error(error.message || 'Failed to update task due date')
    }
  }

  const handleTaskRoleChange = async (taskId: string, role: string) => {
    const newRole = role === 'none' ? undefined : (role as 'pilot' | 'editor' | 'accounts');
    try {
      // Optimistic update
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === taskId
            ? { ...t, assigned_role: newRole, updated_at: new Date().toISOString() }
            : t
        )
      )

      await tasksApi.update(taskId, { assigned_role: newRole })
      toast.success('Task role updated successfully')
    } catch (error: any) {
      // Revert on error
      fetchProjectData()
      toast.error(error.message || 'Failed to update task role')
    }
  }

  // Sort and filter shoots
  const getSortedAndFilteredSchedules = () => {
    let filteredSchedules = schedules

    // Apply status filter
    if (shootStatusFilter !== 'all') {
      filteredSchedules = schedules.filter(schedule => schedule.status === shootStatusFilter)
    }

    // Apply sorting
    return filteredSchedules.sort((a, b) => {
      let comparison = 0

      switch (shootSortBy) {
        case 'date':
          comparison = new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime()
          break
        case 'status':
          comparison = a.status.localeCompare(b.status)
          break
        case 'amount':
          const aAmount = a.amount || 0
          const bAmount = b.amount || 0
          comparison = aAmount - bAmount
          break
        default:
          comparison = 0
      }

      return shootSortOrder === 'asc' ? comparison : -comparison
    })
  }

  const sortedAndFilteredSchedules = getSortedAndFilteredSchedules()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">Project not found</h2>
          <p className="text-muted-foreground mb-4">The project you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/projects')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="relative flex items-center justify-center">
        {/* Back Button - Absolute positioned to left */}
        <div className="absolute left-0">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push('/projects')}
            title="Back to Projects"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
        </div>

        {/* Centered Title Section */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <h1 className="text-3xl font-bold text-foreground">{project.name}</h1>
            {project.custom_id && (
              <span
                className="px-2 py-0.5 rounded border border-border text-[10px] text-muted-foreground bg-muted/50"
                title="Project ID"
              >
                {project.custom_id}
              </span>
            )}
          </div>
          <div className="flex items-center justify-center space-x-2 text-muted-foreground">
            <Building className="w-4 h-4" />
            <Link href={`/clients/${project.client?.id}`} className="hover:text-blue-600">
              {project.client?.name}
            </Link>
            <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${
              project.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
              project.status === 'completed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
              project.status === 'on_hold' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
              'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
            }`}>
              {project.status.replace('_', ' ')}
            </span>
          </div>
          {project.location && (
            <div className="flex items-center justify-center mt-2 text-sm text-muted-foreground">
              <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
              <a
                href={formatGoogleMapsUrl(project.location)}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                {project.location}
              </a>
            </div>
          )}
        </div>

        {/* Edit Button - Absolute positioned to right */}
        <div className="absolute right-0">
          <Button onClick={() => setIsEditModalOpen(true)}>
            <Edit className="w-4 h-4 mr-2" />
            Edit Project
          </Button>
        </div>
      </div>

      {/* Financial Summary Card */}
      <div className="bg-card border border-border rounded-xl p-4">
        <h3 className="text-lg font-semibold text-foreground mb-4">Financial Summary</h3>

        {/* Revenue Section with Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-foreground">Revenue</span>
            </div>
            <span className="text-lg font-bold text-foreground">₹{project.total_amount.toLocaleString()}</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${project.total_amount > 0 ? (project.amount_received / project.total_amount) * 100 : 0}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Received: ₹{project.amount_received.toLocaleString()}</span>
            <span>Pending: ₹{project.amount_pending.toLocaleString()}</span>
          </div>
        </div>

        {/* Costs Section */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Receipt className="w-3 h-3 text-purple-600" />
              <span className="text-xs text-muted-foreground">Expenses</span>
            </div>
            <p className="text-sm font-semibold text-foreground">₹{totalExpenses.toLocaleString()}</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Building className="w-3 h-3 text-orange-600" />
              <span className="text-xs text-muted-foreground">Outsourcing</span>
            </div>
            <p className="text-sm font-semibold text-foreground">₹{totalOutsourcing.toLocaleString()}</p>
            {totalOutsourcing > 0 && (
              <div className="mt-1">
                {/* Progress bar showing actual payments made vs total outsourcing costs */}
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                  <div
                    className="bg-green-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${totalOutsourcing > 0 ? Math.min((outsourcingExpenses / totalOutsourcing) * 100, 100) : 0}%` }}
                  ></div>
                </div>
                {/* Show payment breakdown */}
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>Paid: ₹{outsourcingExpenses.toLocaleString()}</span>
                  <span>Pending: ₹{Math.max(0, totalOutsourcing - outsourcingExpenses).toLocaleString()}</span>
                </div>
              </div>
            )}
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <TrendingUp className={`w-3 h-3 ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`} />
              <span className="text-xs text-muted-foreground">{profit >= 0 ? 'Profit' : 'Loss'}</span>
            </div>
            <p className={`text-sm font-semibold ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              ₹{Math.abs(profit).toLocaleString()}
            </p>
          </div>
        </div>

        {/* GST Info */}
        <div className="pt-3 border-t border-border">
          <p className="text-xs text-muted-foreground text-center">
            GST {project.gst_inclusive ? 'Inclusive' : 'Exclusive'}
          </p>
        </div>
      </div>

      {/* Project Stats Card */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4">Project Statistics</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Total Schedules</span>
            <span className="font-semibold text-foreground">{schedules.length}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Completed Schedules</span>
            <span className="font-semibold text-foreground">
              {schedules.filter((s: any) => s.status === 'completed').length}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Total Payments</span>
            <span className="font-semibold text-foreground">{payments.length}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Open Tasks</span>
            <span className="font-semibold text-foreground">
              {tasks.filter(t => t.status !== 'completed' && t.status !== 'cancelled').length}
            </span>
          </div>
        </div>
      </div>
      {/* Shoots Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4">
          <h3 className="text-lg font-semibold text-foreground">Schedules</h3>
          <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
            {/* Filters and Sorting */}
            {schedules.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {/* Status Filter */}
                <select
                  value={shootStatusFilter}
                  onChange={(e) => setShootStatusFilter(e.target.value)}
                  className="text-sm border border-border rounded-md px-3 py-1.5 bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="all">All Status</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="rescheduled">Rescheduled</option>
                </select>

                {/* Sort By */}
                <select
                  value={shootSortBy}
                  onChange={(e) => setShootSortBy(e.target.value as 'date' | 'status' | 'amount')}
                  className="text-sm border border-border rounded-md px-3 py-1.5 bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="date">Sort by Date</option>
                  <option value="status">Sort by Status</option>
                  <option value="amount">Sort by Amount</option>
                </select>

                {/* Sort Order */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShootSortOrder(shootSortOrder === 'asc' ? 'desc' : 'asc')}
                  className="text-sm px-3 py-1.5 h-auto"
                >
                  <ArrowUpDown className="w-3 h-3 mr-1" />
                  {shootSortOrder === 'asc' ? 'Asc' : 'Desc'}
                </Button>
              </div>
            )}

            <Button
              onClick={() => setIsAddShootModalOpen(true)}
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Schedule
            </Button>
          </div>
        </div>
        {schedules.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No schedules created for this project.</p>
          </div>
        ) : sortedAndFilteredSchedules.length === 0 ? (
          <div className="text-center py-8">
            <Filter className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No schedules match the current filters.</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setShootStatusFilter('all')
                setShootSortBy('date')
                setShootSortOrder('desc')
              }}
              className="mt-2"
            >
              Clear Filters
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sortedAndFilteredSchedules.map((schedule) => (
              <ScheduleCard
                key={schedule.id}
                schedule={schedule}
                onEdit={setEditingShoot}
                onStatusChange={handleShootStatusChange}
                onCancel={handleCancelShoot}
              />
            ))}
          </div>
        )}

        {/* Schedules Summary */}
        {schedules.length > 0 && (
          <div className="mt-6 pt-4 border-t border-border">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-foreground">
                  {sortedAndFilteredSchedules.length}
                </div>
                <div className="text-sm text-muted-foreground">
                  {shootStatusFilter === 'all' ? 'Total Schedules' : `${shootStatusFilter} Schedules`}
                </div>
              </div>
              <div>
                <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                  {sortedAndFilteredSchedules.filter(s => s.status === 'completed').length}
                </div>
                <div className="text-sm text-muted-foreground">Completed</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                  {sortedAndFilteredSchedules.filter(s => s.status === 'scheduled').length}
                </div>
                <div className="text-sm text-muted-foreground">Scheduled</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
                  ₹{sortedAndFilteredSchedules
                    .filter(s => s.amount)
                    .reduce((sum, s) => sum + (s.amount || 0), 0)
                    .toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Total Amount</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Expenses Section */}
      <div className="bg-card border border-border rounded-lg overflow-hidden">
        {/* Header */}
        <div className="app-card-header">
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                  <Receipt className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-foreground">Project Expenses</h3>
                  <p className="text-sm text-muted-foreground">
                    Track and manage project costs
                  </p>
                </div>
              </div>

              {/* Summary Stats */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-2 sm:space-y-0 mt-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-sm text-muted-foreground">Total Spent:</span>
                  <span className="text-lg font-bold text-red-600 dark:text-red-400">
                    ₹{totalExpenses.toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-muted-foreground">Entries:</span>
                  <span className="text-sm font-semibold text-foreground">
                    {expenses.length} expense{expenses.length !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>
            </div>

            <Button
              onClick={() => setIsAddExpenseModalOpen(true)}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Expense
            </Button>
          </div>
        </div>

        {/* Content Area */}
        <div className="p-6">
          {expensesLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="relative">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-200 dark:border-purple-800"></div>
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-600 border-t-transparent absolute top-0 left-0"></div>
              </div>
            </div>
          ) : expensesError ? (
            <div className="text-center py-12">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-full w-fit mx-auto mb-4">
                <AlertCircle className="w-8 h-8 text-red-500" />
              </div>
              <h4 className="text-lg font-semibold text-foreground mb-2">Error Loading Expenses</h4>
              <p className="text-muted-foreground">{expensesError}</p>
            </div>
          ) : expenses.length === 0 ? (
            <div className="text-center py-12">
              <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-full w-fit mx-auto mb-4">
                <Receipt className="w-12 h-12 text-purple-400" />
              </div>
              <h4 className="text-lg font-semibold text-foreground mb-2">No Expenses Yet</h4>
              <p className="text-muted-foreground mb-4">Start tracking project costs by adding your first expense.</p>
              <Button
                onClick={() => setIsAddExpenseModalOpen(true)}
                variant="outline"
                className="border-purple-200 dark:border-purple-800 text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add First Expense
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {expenses.map((expense, index) => (
                <div
                  key={expense.id}
                  className="transform transition-all duration-200 hover:scale-[1.02]"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <ExpenseCard
                    expense={expense}
                    onEdit={handleEditExpense}
                    onDelete={handleDeleteExpense}
                    compact={true}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Payments Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-foreground">Payments</h3>
          <Button
            onClick={() => setIsAddPaymentModalOpen(true)}
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Record Payment
          </Button>
        </div>
        {payments.length === 0 ? (
          <div className="text-center py-8">
            <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No payments recorded for this project.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {payments
              .sort((a, b) => new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime())
              .map((payment) => (
                <div key={payment.id} className="border border-border rounded-lg p-4 hover:bg-muted/50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-foreground">₹{payment.amount.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(payment.payment_date).toLocaleDateString()} •
                        <span className="capitalize ml-1">
                          {payment.payment_method.replace('_', ' ')}
                        </span>
                      </p>
                      {payment.reference_number && (
                        <p className="text-xs text-muted-foreground">Ref: {payment.reference_number}</p>
                      )}
                      {payment.notes && (
                        <p className="text-xs text-muted-foreground mt-1">{payment.notes}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>

      {/* Vendor Payment Section */}
      {project.vendor_payment_amount && project.vendor_payment_amount > 0 ? (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-foreground">Vendor Payment</h3>
            <Button
              onClick={() => setIsVendorPaymentModalOpen(true)}
              size="sm"
              variant="outline"
            >
              <Edit className="w-4 h-4 mr-2" />
              Update Payment
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* Payment Amount */}
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <DollarSign className="w-4 h-4 text-blue-600" />
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Amount</span>
              </div>
              <div className="text-xl font-bold text-foreground">
                ₹{project.vendor_payment_amount.toLocaleString()}
              </div>
            </div>

            {/* Payment Status */}
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Status</span>
              </div>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${
                project.vendor_payment_status === 'paid' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                project.vendor_payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                project.vendor_payment_status === 'overdue' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
              }`}>
                {project.vendor_payment_status === 'paid' ? '✓ Paid' :
                 project.vendor_payment_status === 'pending' ? '⏳ Pending' :
                 project.vendor_payment_status === 'overdue' ? '⚠️ Overdue' :
                 '❌ Cancelled'}
              </div>
            </div>

            {/* Due Date */}
            {project.vendor_payment_due_date && (
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Clock className="w-4 h-4 text-orange-600" />
                  <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Due Date</span>
                </div>
                <div className="text-sm font-medium text-foreground">
                  {new Date(project.vendor_payment_due_date).toLocaleDateString()}
                </div>
              </div>
            )}

            {/* Payment Date */}
            {project.vendor_payment_date && (
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Calendar className="w-4 h-4 text-green-600" />
                  <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Paid On</span>
                </div>
                <div className="text-sm font-medium text-foreground">
                  {new Date(project.vendor_payment_date).toLocaleDateString()}
                </div>
              </div>
            )}
          </div>

          {/* Payment Notes */}
          {project.vendor_payment_notes && (
            <div className="border border-border rounded-lg p-4 bg-muted/30">
              <h4 className="text-sm font-medium text-foreground mb-2">Payment Notes</h4>
              <p className="text-sm text-muted-foreground">{project.vendor_payment_notes}</p>
            </div>
          )}
        </div>
      ) : null}

      {/* Tasks Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Tasks</h3>
            <p className="text-sm text-muted-foreground">
              Track project progress with role-based task management
            </p>
          </div>
          <Button
            onClick={() => setIsAddTaskModalOpen(true)}
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Task
          </Button>
        </div>

        {tasks.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {project?.client?.client_type
                ? `Default tasks will be auto-generated for ${project.client.client_type} projects.`
                : 'No tasks created for this project.'
              }
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              Tasks are filtered based on your role and assignments.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Group tasks by shoots */}
            {(() => {
              // Group tasks by shoot_id
              const taskGroups: Record<string, typeof tasks> = {}
              const projectTasks: typeof tasks = []

              tasks.forEach(task => {
                if (task.shoot_id) {
                  if (!taskGroups[task.shoot_id]) {
                    taskGroups[task.shoot_id] = []
                  }
                  taskGroups[task.shoot_id].push(task)
                } else {
                  projectTasks.push(task)
                }
              })

              return (
                <>
                  {/* Shoot-based task groups */}
                  {Object.entries(taskGroups).map(([shootId, shootTasks]) => {
                    const shoot = schedules.find((s: any) => s.id === shootId)
                    return (
                      <div key={shootId} className="border border-border rounded-lg p-4 bg-muted/20">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <Camera className="w-4 h-4 text-muted-foreground" />
                            <h4 className="font-medium text-foreground">
                              {shoot ? `Schedule - ${formatDate(shoot.scheduled_date)}` : 'Unknown Schedule'}
                            </h4>
                            {shoot && (
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                shoot.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' :
                                shoot.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300' :
                                shoot.status === 'scheduled' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' :
                                'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
                              }`}>
                                {shoot.status}
                              </span>
                            )}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {shootTasks.length} task{shootTasks.length !== 1 ? 's' : ''}
                          </span>
                        </div>
                        <div className="space-y-2">
                          {shootTasks.map((task) => (
                            <RedesignedTaskCard
                              key={task.id}
                              task={task}
                              users={users}
                              onEdit={handleEditTask}
                              onDelete={handleDeleteTask}
                              onStatusChange={handleTaskStatusChange}
                              onAssignmentChange={handleAssignmentChange}
                              onDueDateChange={handleDueDateChange}
                              onInlineUpdate={handleTaskInlineUpdate}
                              onRoleChange={handleTaskRoleChange}
                              onCompleteSchedule={setCompletingShoot}
                              schedule={shoot}
                              compact={true}
                              showRoleFilter={true}
                              currentUserRole={user?.role}
                            />
                          ))}
                        </div>
                      </div>
                    )
                  })}

                  {/* Project-level tasks */}
                  {projectTasks.length > 0 && (
                    <div className="border border-border rounded-lg p-4 bg-primary/5">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Building className="w-4 h-4 text-muted-foreground" />
                          <h4 className="font-medium text-foreground">Project Tasks</h4>
                          <span className="text-xs text-muted-foreground">(Shared across all shoots)</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {projectTasks.length} task{projectTasks.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                      <div className="space-y-2">
                        {projectTasks.map((task) => (
                          <RedesignedTaskCard
                            key={task.id}
                            task={task}
                            users={users}
                            onEdit={handleEditTask}
                            onDelete={handleDeleteTask}
                            onStatusChange={handleTaskStatusChange}
                            onAssignmentChange={handleAssignmentChange}
                            onDueDateChange={handleDueDateChange}
                            onInlineUpdate={handleTaskInlineUpdate}
                            onRoleChange={handleTaskRoleChange}
                            onCompleteSchedule={setCompletingShoot}
                            schedule={null} // Project tasks don't have associated schedules
                            compact={true}
                            showRoleFilter={true}
                            currentUserRole={user?.role}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )
            })()}
          </div>
        )}
      </div>

      {/* Vendor Payments Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-foreground">Vendor Payments</h3>
        </div>
        {vendors.length === 0 ? (
          <div className="text-center py-8">
            <Building2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No vendors linked to this project.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {vendors.map((vendor) => {
              const { totalOwed, totalPaid } = getVendorPaymentData(vendor.id)
              return (
                <VendorPaymentCard
                  key={vendor.id}
                  vendor={vendor}
                  onRecordPayment={handleRecordVendorPayment}
                  compact
                  totalOwed={totalOwed}
                  totalPaid={totalPaid}
                  projectCustomId={project.custom_id || project.id}
                />
              )
            })}
          </div>
        )}
      </div>

      {/* Modals */}
      {/* Edit Project Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Project"
        size="lg"
      >
        <ProjectForm
          project={project}
          onSuccess={handleEditSuccess}
          onCancel={() => setIsEditModalOpen(false)}
        />
      </Modal>

      {/* Add Shoot Modal */}
      <Modal
        isOpen={isAddShootModalOpen}
        onClose={() => setIsAddShootModalOpen(false)}
        title="Schedule New Shoot"
        size="xl"
      >
        <ScheduleForm
          onSuccess={handleAddScheduleSuccess}
          onCancel={() => setIsAddShootModalOpen(false)}
          preselectedProjectId={projectId}
        />
      </Modal>

      {/* Edit Schedule Modal */}
      <Modal
        isOpen={!!editingShoot}
        onClose={() => setEditingShoot(null)}
        title="Edit Schedule"
        size="xl"
      >
        {editingShoot && (
          <ScheduleForm
            schedule={editingShoot}
            onSuccess={handleEditShootSuccess}
            onCancel={() => setEditingShoot(null)}
          />
        )}
      </Modal>

      {/* Add Payment Modal */}
      <Modal
        isOpen={isAddPaymentModalOpen}
        onClose={() => setIsAddPaymentModalOpen(false)}
        title="Record Payment"
        size="lg"
      >
        <PaymentForm
          onSuccess={handleAddPaymentSuccess}
          onCancel={() => setIsAddPaymentModalOpen(false)}
          preselectedProjectId={projectId}
        />
      </Modal>

      {/* Add Task Modal */}
      <Modal
          isOpen={isAddTaskModalOpen}
          onClose={() => setIsAddTaskModalOpen(false)}
          title="Add New Task"
          size="lg"
        >
          <TaskForm
            onSuccess={handleAddTaskSuccess}
            onCancel={() => setIsAddTaskModalOpen(false)}
            preselectedProjectId={projectId}
          />
        </Modal>

        {/* Add Expense Modal */}
        <Modal
          isOpen={isAddExpenseModalOpen}
          onClose={() => setIsAddExpenseModalOpen(false)}
          title="Add Project Expense"
          size="lg"
        >
          <AddExpenseForm
            projectId={project.id}
            onSuccess={() => {
              setIsAddExpenseModalOpen(false);
              refetchExpenses();
              toast.success("Expense added successfully");
            }}
            onCancel={() => setIsAddExpenseModalOpen(false)}
          />
        </Modal>

        {/* Edit Expense Modal */}
        <Modal
          isOpen={isEditExpenseModalOpen}
          onClose={() => {
            setIsEditExpenseModalOpen(false)
            setEditingExpense(null)
          }}
          title="Edit Expense"
          size="lg"
        >
          {editingExpense && (
            <ExpenseForm
              expense={editingExpense}
              onSuccess={handleEditExpenseSuccess}
              onCancel={() => {
                setIsEditExpenseModalOpen(false)
                setEditingExpense(null)
              }}
              preselectedProjectId={project.id}
            />
          )}
        </Modal>

        {/* Vendor Payment Modal */}
        <Modal
          isOpen={isVendorPaymentModalOpen}
          onClose={() => setIsVendorPaymentModalOpen(false)}
          title="Manage Vendor Payment"
          size="lg"
        >
          <VendorPaymentForm
            project={project}
            onSuccess={() => {
              setIsVendorPaymentModalOpen(false)
              fetchProjectData()
            }}
            onCancel={() => setIsVendorPaymentModalOpen(false)}
          />
        </Modal>

        {/* Record Vendor Payment Modal */}
        <Modal
          isOpen={isRecordVendorPaymentModalOpen}
          onClose={() => {
            setIsRecordVendorPaymentModalOpen(false)
            setSelectedVendor(null)
          }}
          title="Record Vendor Payment"
          size="lg"
        >
          {selectedVendor && (
            <RecordVendorPaymentForm
              vendor={selectedVendor}
              projectId={project.id}
              onSuccess={handleVendorPaymentSuccess}
              onCancel={() => {
                setIsRecordVendorPaymentModalOpen(false)
                setSelectedVendor(null)
              }}
            />
          )}
        </Modal>

        {/* Shoot Completion Form */}
        <ShootCompletionForm
          schedule={completingShoot}
          shootTask={tasks.find(task => task.title.toLowerCase() === 'shoot' && task.shoot_id === completingShoot?.id)}
          isOpen={!!completingShoot}
          onClose={() => setCompletingShoot(null)}
          onComplete={handleCompleteShoot}
        />
    </div>
  )
}
