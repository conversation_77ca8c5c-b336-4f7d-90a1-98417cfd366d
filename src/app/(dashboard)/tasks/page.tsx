'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Modal } from '@/components/ui/modal'
import { TaskForm } from '@/components/forms/TaskForm'
import { TaskCard } from '@/components/ui/task-card'
import { RedesignedTaskCard } from '@/components/ui/redesigned-task-card'
import { ShootCompletionForm, type ShootCompletionData } from '@/components/ui/schedule-completion-form'
import { useTasks, useUpdateTask, useDeleteTask, useSchedules, useUpdateSchedule, useUsers } from '@/hooks/useApi'
import { Search, Plus, CheckSquare, Clock, AlertTriangle, Grid, List, Filter } from 'lucide-react'
import toast from 'react-hot-toast'
import type { Task, Schedule } from '@/types'
import { tasksApi, projectsApi } from '@/lib/api'

export default function TasksPage() {
  const { data: tasks, loading, refetch } = useTasks()
  const { updateTask } = useUpdateTask()
  const { deleteTask } = useDeleteTask()
  const { data: schedules } = useSchedules()
  const { updateSchedule } = useUpdateSchedule()
  const { data: users } = useUsers()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [completingSchedule, setCompletingSchedule] = useState<Schedule | null>(null)

  // Filter and sort tasks based on search and filters
  const filteredTasks = tasks?.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.project?.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || task.status === statusFilter
    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  }).sort((a, b) => {
    // Enhanced sorting: schedule tasks first, then project tasks
    const aIsProjectTask = !a.shoot_id
    const bIsProjectTask = !b.shoot_id

    // If one is project task and other is schedule task, schedule task comes first
    if (aIsProjectTask && !bIsProjectTask) return 1
    if (!aIsProjectTask && bIsProjectTask) return -1

    // Both are same type, sort by order field
    const aOrder = a.order ?? (aIsProjectTask ? 1000 : 0)
    const bOrder = b.order ?? (bIsProjectTask ? 1000 : 0)

    if (aOrder !== bOrder) {
      return aOrder - bOrder
    }

    // Fallback to creation time
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  }) || []

  // Group tasks by status for overview
  const tasksByStatus = {
    pending: filteredTasks.filter(t => t.status === 'pending'),
    in_progress: filteredTasks.filter(t => t.status === 'in_progress'),
    completed: filteredTasks.filter(t => t.status === 'completed'),
    cancelled: filteredTasks.filter(t => t.status === 'cancelled'),
  }

  // Get overdue tasks
  const overdueTasks = filteredTasks.filter(task => 
    task.due_date && 
    new Date(task.due_date) < new Date() && 
    task.status !== 'completed'
  )

  const handleTaskSuccess = () => {
    setIsTaskModalOpen(false)
    setEditingTask(null)
    // Don't refetch - let the user refresh manually if needed
  }

  const handleCompleteSchedule = async (completionData: ShootCompletionData) => {
    if (!completingSchedule) return

    try {
      const updates = {
        status: 'completed',
        actual_date: new Date().toISOString(),
        device_used: completionData.device_used,
        battery_count: completionData.battery_count,
        shoot_start_time: completionData.shoot_start_time,
        shoot_end_time: completionData.shoot_end_time,
        completion_notes: completionData.completion_notes
      }

      // Complete the schedule first
      await updateSchedule(completingSchedule.id, updates)

      // Then complete all schedule-related tasks
      await projectsApi.completeShootTasks(completingSchedule.id)

      toast.success('Schedule and all related tasks completed successfully')
      setCompletingSchedule(null)
      // Don't refetch - let the user refresh manually if needed
    } catch (error: any) {
      toast.error(error.message || 'Failed to complete schedule')
    }
  }

  const handleAssignmentChange = async (taskId: string, userId: string) => {
    try {
      await tasksApi.updateAssignment(taskId, userId)
      toast.success('Task assignment updated successfully')
    } catch (error: any) {
      toast.error(error.message || 'Failed to update task assignment')
    }
  }

  const handleDueDateChange = async (taskId: string, dueDate: string | null) => {
    const task = localTasks.find(t => t.id === taskId)
    if (!task) return

    // Optimistic update
    const updatedTask = { ...task, due_date: dueDate }
    setLocalTasks(prevTasks =>
      prevTasks.map(t => t.id === taskId ? updatedTask : t)
    )

    try {
      await tasksApi.updateDueDate(taskId, dueDate)
      toast.success('Task due date updated successfully')
    } catch (error: any) {
      // Revert optimistic update on error
      setLocalTasks(prevTasks =>
        prevTasks.map(t => t.id === taskId ? task : t)
      )
      toast.error(error.message || 'Failed to update task due date')
    }
  }

  const handleRoleChange = async (taskId: string, role: string) => {
    try {
      const actualRole = role === 'none' ? null : role
      await updateTask(taskId, { assigned_role: actualRole })
      toast.success('Task role updated successfully')
    } catch (error: any) {
      toast.error(error.message || 'Failed to update task role')
    }
  }

  // Function to get schedule for a task
  const getScheduleForTask = (task: Task): Schedule | undefined => {
    if (!schedules) return undefined

    // First try to match by shoot_id if it exists
    if (task.shoot_id) {
      const scheduleById = schedules.find(schedule => schedule.id === task.shoot_id)
      if (scheduleById) return scheduleById
    }

    // If no shoot_id or no match, try to find by project_id for project-level tasks
    if (task.project_id) {
      const scheduleByProject = schedules.find(schedule =>
        schedule.project_id === task.project_id &&
        schedule.status !== 'completed' &&
        schedule.status !== 'cancelled'
      )
      if (scheduleByProject) return scheduleByProject
    }

    return undefined
  }

  const handleStatusChange = async (task: Task, newStatus: Task['status']) => {
    try {
      await updateTask(task.id, {
        status: newStatus,
        ...(newStatus === 'in_progress' && { started_at: new Date().toISOString() }),
        ...(newStatus === 'completed' && { completed_at: new Date().toISOString() })
      })
      toast.success(`Task marked as ${newStatus.replace('_', ' ')}`)
      // Let the hook handle the update
    } catch (error: any) {
      toast.error(error.message || 'Failed to update task status')
    }
  }

  const handleDeleteTask = async (task: Task) => {
    try {
      await deleteTask(task.id)
      toast.success('Task deleted successfully')
      // Don't refetch - let the user refresh manually if needed
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete task')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading tasks...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">


      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Task Management</h1>
          <p className="text-muted-foreground">Organize and track your work</p>
        </div>
        <Button onClick={() => setIsTaskModalOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Task
        </Button>
      </div>

      {/* Task Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-muted rounded-lg">
              <Clock className="w-6 h-6 text-muted-foreground" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Pending</p>
              <p className="text-2xl font-bold text-foreground">{tasksByStatus.pending.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <CheckSquare className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">In Progress</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{tasksByStatus.in_progress.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <CheckSquare className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Completed</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{tasksByStatus.completed.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Overdue</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{overdueTasks.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col lg:flex-row gap-3 items-start lg:items-center justify-between">
        <div className="w-full flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
          {/* Search */}
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-11"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="flex h-11 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 w-full sm:w-auto"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>

          {/* Priority Filter */}
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="flex h-11 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 w-full sm:w-auto"
          >
            <option value="all">All Priority</option>
            <option value="urgent">Urgent</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Active Filters Display */}
      {(statusFilter !== 'all' || priorityFilter !== 'all' || searchTerm) && (
        <div className="flex items-center space-x-2 text-sm">
          <Filter className="w-4 h-4 text-muted-foreground" />
          <span className="text-muted-foreground">Active filters:</span>
          {statusFilter !== 'all' && (
            <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 rounded-full text-xs">
              Status: {statusFilter.replace('_', ' ')}
            </span>
          )}
          {priorityFilter !== 'all' && (
            <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300 rounded-full text-xs">
              Priority: {priorityFilter}
            </span>
          )}
          {searchTerm && (
            <span className="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 rounded-full text-xs">
              Search: "{searchTerm}"
            </span>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setStatusFilter('all')
              setPriorityFilter('all')
              setSearchTerm('')
            }}
            className="text-xs"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Tasks Grid/List */}
      <div className={
        viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
      }>
        {filteredTasks.map((task) => (
          <RedesignedTaskCard
            key={task.id}
            task={task}
            users={users || []}
            onEdit={setEditingTask}
            onDelete={handleDeleteTask}
            onStatusChange={handleStatusChange}
            onAssignmentChange={handleAssignmentChange}
            onRoleChange={handleRoleChange}
            onDueDateChange={handleDueDateChange}
            onCompleteSchedule={(schedule) => setCompletingSchedule(schedule)}
            schedule={getScheduleForTask(task)}
            compact={viewMode === 'list'}
          />
        ))}
        {filteredTasks.length === 0 && (
          <div className="col-span-full text-center py-12">
            <CheckSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No tasks found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all' 
                ? 'Try adjusting your search terms or filters' 
                : 'Start by creating your first task'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && priorityFilter === 'all' && (
              <Button onClick={() => setIsTaskModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Task
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Task Modal */}
      <Modal
        isOpen={isTaskModalOpen || !!editingTask}
        onClose={() => {
          setIsTaskModalOpen(false)
          setEditingTask(null)
        }}
        title={editingTask ? 'Edit Task' : 'Create New Task'}
        size="lg"
      >
        <TaskForm
          task={editingTask || undefined}
          onSuccess={handleTaskSuccess}
          onCancel={() => {
            setIsTaskModalOpen(false)
            setEditingTask(null)
          }}
        />
      </Modal>

      {/* Schedule Completion Form */}
      <ShootCompletionForm
        schedule={completingSchedule}
        shootTask={tasks.find(task => task.title.toLowerCase() === 'shoot' && task.shoot_id === completingSchedule?.id)}
        isOpen={!!completingSchedule}
        onClose={() => setCompletingSchedule(null)}
        onComplete={handleCompleteSchedule}
      />
    </div>
  )
}
