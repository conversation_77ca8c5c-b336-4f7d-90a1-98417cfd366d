import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET() {
  try {
    // Create a simple Supabase client for testing with service role
    const supabase = createServerClient(supabaseUrl, supabaseServiceKey, {
      cookies: {
        getAll() {
          return []
        },
        setAll() {
          // No-op for testing
        },
      },
    })

    // First, get a valid project ID
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, custom_id, name, clients(id, custom_id, name)')
      .limit(1)

    if (projectError || !projects || projects.length === 0) {
      return NextResponse.json({ 
        error: 'No projects found',
        details: projectError?.message 
      }, { status: 400 })
    }

    const project = projects[0]

    // Get a valid vendor ID
    const { data: vendors, error: vendorError } = await supabase
      .from('outsourcing_vendors')
      .select('id')
      .limit(1)

    if (vendorError || !vendors || vendors.length === 0) {
      return NextResponse.json({ 
        error: 'No vendors found',
        details: vendorError?.message 
      }, { status: 400 })
    }

    const vendor = vendors[0]

    // Create a test schedule using the create_schedule_with_vendors function
    const { data: result, error: createError } = await supabase
      .rpc('create_schedule_with_vendors', {
        p_project_id: project.id,
        p_scheduled_date: new Date().toISOString(),
        p_scheduled_end_date: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours later
        p_pilot_id: null,
        p_amount: 1000,
        p_location: 'Test Location',
        p_google_maps_link: null,
        p_notes: 'Test schedule created via API',
        p_is_recurring: false,
        p_recurring_pattern: null,
        p_is_outsourced: true,
        p_vendors: [
          {
            vendor_id: vendor.id,
            cost: 500,
            notes: 'Test vendor assignment'
          }
        ]
      })

    if (createError) {
      return NextResponse.json({ 
        error: 'Failed to create schedule',
        details: createError.message 
      }, { status: 500 })
    }

    // Get the created schedule with SharePoint data
    const { data: schedule, error: fetchError } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        scheduled_date,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link,
        projects (
          id,
          custom_id,
          name,
          sharepoint_folder_id,
          sharepoint_folder_url,
          sharepoint_share_link,
          clients (
            id,
            custom_id,
            name,
            sharepoint_folder_id,
            sharepoint_folder_url,
            sharepoint_share_link
          )
        )
      `)
      .eq('id', result)
      .single()

    if (fetchError) {
      return NextResponse.json({ 
        error: 'Schedule created but failed to fetch details',
        scheduleId: result,
        details: fetchError.message 
      }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Test schedule created successfully',
      schedule: schedule,
      project: project,
      vendor: vendor
    })

  } catch (error) {
    console.error('Error in test-create-schedule-simple:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}