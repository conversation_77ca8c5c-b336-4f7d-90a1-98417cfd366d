import { NextResponse } from 'next/server';
import { createProjectFolderStructure } from '@/lib/microsoft-graph';

export async function GET() {
  try {
    // Test with sample data and a dummy schedule ID
    const result = await createProjectFolderStructure(
      '00000000-0000-0000-0000-000000000000', // Dummy schedule ID for testing
      'Reliance_Solar_Farm',
      'DroneSurveyZone7',
      '2025-08-05'
    );
    
    return NextResponse.json({
      success: true,
      message: 'Folder structure created successfully',
      data: result
    });
  } catch (error: any) {
    console.error('Error in test Microsoft Graph API:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to create folder structure'
      },
      { status: 500 }
    );
  }
}