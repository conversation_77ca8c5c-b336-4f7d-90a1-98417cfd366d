import { NextRequest, NextResponse } from 'next/server'
import { schedulesApi } from '@/lib/api'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing SharePoint Fix: Using schedulesApi.createWithVendors...')
    console.log(`📋 SHAREPOINT_FOLDER_CREATION_MODE: ${process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'undefined (defaults to background-with-fallback)'}`)
    
    // Use service role client to find test data
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Find a project and user
    const { data: projects } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1)
    
    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
    
    if (!projects?.length || !users?.length) {
      return NextResponse.json(
        { error: 'No projects or users found' },
        { status: 400 }
      )
    }
    
    const project = projects[0]
    const user = users[0]
    
    console.log(`📋 Using project: ${project.custom_id} ${project.name}`)
    console.log(`👤 Using user: ${user.name}`)
    
    // Create schedule using the ACTUAL schedulesApi.createWithVendors method
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      pilot_id: user.id,
      amount: 1500,
      location: 'Test Location - SharePoint Fix Verification',
      google_maps_link: 'https://maps.google.com',
      notes: 'Testing SharePoint folder creation fix with sync mode',
      is_recurring: false,
      recurring_pattern: undefined,
      is_outsourced: false
    }

    const vendors: any[] = []
    
    console.log('🚀 Creating schedule via schedulesApi.createWithVendors (this should trigger SharePoint folder creation)...')
    
    // Use the ACTUAL schedulesApi.createWithVendors method that includes SharePoint logic
    let schedule
    try {
      schedule = await schedulesApi.createWithVendors(scheduleData, vendors)
      console.log(`✅ Schedule created via schedulesApi.createWithVendors: ${schedule.custom_id}`)
      console.log(`🆔 Schedule ID: ${schedule.id}`)
    } catch (createError) {
      console.error('❌ Error in schedulesApi.createWithVendors:', createError)
      
      // If the error is from getById, let's try to find the schedule directly
      if (createError && typeof createError === 'object' && 'code' in createError && createError.code === 'PGRST116') {
        console.log('🔍 getById failed, trying to find the schedule directly...')
        
        // Try to find the most recently created schedule for this project
        const { data: recentSchedules, error: searchError } = await supabase
          .from('schedules')
          .select('id, custom_id, project_id, scheduled_date, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
          .eq('project_id', project.id)
          .order('created_at', { ascending: false })
          .limit(1)
        
        if (searchError) {
          console.error('❌ Error searching for recent schedules:', searchError)
          throw createError
        }
        
        if (recentSchedules && recentSchedules.length > 0) {
          schedule = recentSchedules[0]
          console.log(`🔍 Found recent schedule: ${schedule.custom_id} (ID: ${schedule.id})`)
        } else {
          console.error('❌ No recent schedules found')
          throw createError
        }
      } else {
        throw createError
      }
    }
    
    // Check SharePoint folder status immediately
    console.log('📁 Checking SharePoint folder status...')
    const { data: updatedSchedule, error: fetchError } = await supabase
      .from('schedules')
      .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', schedule.id)
      .single()
    
    if (fetchError) {
      console.error('❌ Error fetching updated schedule:', fetchError)
    } else {
      console.log('📋 SharePoint status:', {
        folder_id: updatedSchedule.sharepoint_folder_id || 'null',
        folder_url: updatedSchedule.sharepoint_folder_url || 'null',
        share_link: updatedSchedule.sharepoint_share_link || 'null'
      })
    }
    
    return NextResponse.json({
      success: true,
      schedule: {
        id: schedule.id,
        custom_id: schedule.custom_id,
        scheduled_date: schedule.scheduled_date,
        project_id: schedule.project_id,
        sharepoint_folder_id: updatedSchedule?.sharepoint_folder_id || null,
        sharepoint_folder_url: updatedSchedule?.sharepoint_folder_url || null,
        sharepoint_share_link: updatedSchedule?.sharepoint_share_link || null
      },
      sharepoint_status: {
        folder_created: !!updatedSchedule?.sharepoint_folder_id,
        folder_id: updatedSchedule?.sharepoint_folder_id || null,
        folder_url: updatedSchedule?.sharepoint_folder_url || null,
        share_link: updatedSchedule?.sharepoint_share_link || null
      },
      environment: {
        sharepoint_mode: process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'background-with-fallback (default)'
      },
      message: 'Schedule created via schedulesApi.createWithVendors'
    })
    
  } catch (error) {
    console.error('❌ Error in SharePoint fix test:', error)
    console.error('❌ Error details:', JSON.stringify(error, null, 2))
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error
      },
      { status: 500 }
    )
  }
}