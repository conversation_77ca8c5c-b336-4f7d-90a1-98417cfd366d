import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const scheduleId = searchParams.get('id')

    if (!scheduleId) {
      return NextResponse.json({ error: 'Schedule ID is required' }, { status: 400 })
    }

    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    if (!supabaseServiceKey) {
      return NextResponse.json({ error: 'Service key not found' }, { status: 500 })
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      supabaseServiceKey
    )

    const { data: schedule, error } = await supabase
      .from('schedules')
      .select('id, custom_id, scheduled_date, location, notes')
      .eq('id', scheduleId)
      .single()

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch schedule', details: error.message }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Schedule custom_id retrieved',
      schedule
    })

  } catch (error: any) {
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 })
  }
}