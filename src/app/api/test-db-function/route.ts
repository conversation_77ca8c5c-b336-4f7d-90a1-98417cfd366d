import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing database function directly...')
    
    // Use service role client to bypass RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Find a project and user
    const { data: projects } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1)
    
    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
    
    if (!projects?.length || !users?.length) {
      return NextResponse.json(
        { error: 'No projects or users found' },
        { status: 400 }
      )
    }
    
    const project = projects[0]
    const user = users[0]
    
    console.log(`📋 Using project: ${project.custom_id} ${project.name}`)
    console.log(`👤 Using user: ${user.name}`)
    
    const { searchParams } = new URL(request.url)
    const test = searchParams.get('test')

    if (test === 'rls_check') {
      // Check RLS policies on schedules table
      const { data: rlsData, error: rlsError } = await supabase
        .from('pg_policies')
        .select('*')
        .eq('tablename', 'schedules')

      console.log('RLS policies on schedules table:', rlsData)

      // Also check if RLS is enabled
      const { data: tableData, error: tableError } = await supabase
        .rpc('sql', { query: "SELECT relrowsecurity FROM pg_class WHERE relname = 'schedules'" })

      return NextResponse.json({
        success: true,
        rlsPolicies: rlsData,
        rlsEnabled: tableData,
        message: 'RLS check completed'
      })
    }

    // Test the database function directly
    const { data, error } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: project.id,
      p_scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      p_scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      p_pilot_id: user.id,
      p_amount: 1500,
      p_location: 'Test Location Direct DB Call',
      p_google_maps_link: 'https://maps.google.com',
      p_notes: 'Test schedule created via direct DB function call',
      p_is_recurring: false,
      p_recurring_pattern: 'weekly', // Test with weekly
      p_is_outsourced: false,
      p_vendors: []
    })
    
    if (error) {
      console.error('❌ Database function error:', error)
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          details: error
        },
        { status: 500 }
      )
    }
    
    console.log(`✅ Schedule created via direct DB call: ${data?.custom_id}`)
    
    return NextResponse.json({
      success: true,
      schedule: data,
      message: 'Schedule created via direct database function call'
    })
    
  } catch (error) {
    console.error('❌ Error in test endpoint:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}