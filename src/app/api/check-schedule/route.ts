import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    if (!supabaseServiceKey) {
      return NextResponse.json({ error: 'Service key not found' }, { status: 500 })
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      supabaseServiceKey
    )

    const scheduleId = '1348aca1-c61e-48c1-ad22-0293a4f8cac9'

    // Get the schedule with SharePoint data
    const { data: schedule, error } = await supabase
      .from('schedules')
      .select(`
        id,
        scheduled_date,
        location,
        notes,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link,
        projects (
          id,
          name,
          clients (
            id,
            name,
            sharepoint_folder_id,
            sharepoint_folder_url
          )
        )
      `)
      .eq('id', scheduleId)
      .single()

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch schedule', details: error.message }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Schedule details retrieved',
      schedule
    })

  } catch (error: any) {
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 })
  }
}