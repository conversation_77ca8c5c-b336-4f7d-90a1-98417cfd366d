import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const vendorId = searchParams.get('vendorId') || 'fa2e2c4c-7bff-47e5-927c-044c15bd04bb'
    
    const supabase = await createServerSupabaseClient()
    
    console.log('Testing vendor schedules query for vendor:', vendorId)

    // Test the exact query used by the vendor details page
    // First, get schedules with legacy single vendor
    const { data: legacySchedules, error: legacyError } = await supabase
      .from('schedules')
      .select(`
        *,
        project:projects(
          *,
          client:clients(name)
        )
      `)
      .eq('vendor_id', vendorId)
      .eq('is_outsourced', true)

    if (legacyError) {
      console.error('Supabase error (legacy):', legacyError)
      return NextResponse.json({ error: legacyError }, { status: 500 })
    }

    // Then, get schedules with multiple vendors from schedule_vendors junction table
    const { data: junctionSchedules, error: junctionError } = await supabase
      .from('schedules')
      .select(`
        *,
        project:projects(
          *,
          client:clients(name)
        ),
        vendors:schedule_vendors(
          *,
          vendor:outsourcing_vendors(*)
        )
      `)
      .eq('schedule_vendors.vendor_id', vendorId)
      .eq('is_outsourced', true)
      .order('scheduled_date', { ascending: false })

    if (junctionError) {
      console.error('Supabase error (junction):', junctionError)
      return NextResponse.json({ error: junctionError }, { status: 500 })
    }

    // Combine both sets of schedules and remove duplicates
    const allSchedules = [...(legacySchedules || []), ...(junctionSchedules || [])]
    const uniqueSchedules = Array.from(new Map(allSchedules.map(s => [s.id, s])).values())

    // Sort by scheduled date
    uniqueSchedules.sort((a, b) => new Date(b.scheduled_date).getTime() - new Date(a.scheduled_date).getTime())

    console.log('Query result:', { uniqueSchedules })

    return NextResponse.json({
      success: true,
      vendorId,
      schedulesCount: uniqueSchedules?.length || 0,
      schedules: uniqueSchedules
    })
    
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
