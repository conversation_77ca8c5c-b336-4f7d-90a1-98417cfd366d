import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing SharePoint Direct: Testing direct SharePoint folder creation...')
    console.log(`📋 SHAREPOINT_FOLDER_CREATION_MODE: ${process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'undefined'}`)
    
    // Use service role client to find a recent schedule
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Find the most recent schedule
    const { data: schedules, error: scheduleError } = await supabase
      .from('schedules')
      .select('id, custom_id, scheduled_date, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .order('created_at', { ascending: false })
      .limit(1)
    
    if (scheduleError || !schedules?.length) {
      return NextResponse.json(
        { error: 'No schedules found', details: scheduleError },
        { status: 400 }
      )
    }
    
    const schedule = schedules[0]
    console.log(`📋 Using schedule: ${schedule.custom_id} (ID: ${schedule.id})`)
    console.log(`📅 Scheduled date: ${schedule.scheduled_date}`)
    console.log(`📁 Current SharePoint status:`, {
      folder_id: schedule.sharepoint_folder_id || 'null',
      folder_url: schedule.sharepoint_folder_url || 'null',
      share_link: schedule.sharepoint_share_link || 'null'
    })
    
    // Test 1: Try importing background-jobs
    console.log('🔄 Test 1: Importing background-jobs...')
    try {
      const { queueSharePointFolderCreation } = await import('@/lib/background-jobs')
      console.log('✅ background-jobs import successful')
      
      // Test 2: Try calling queueSharePointFolderCreation in sync mode
      console.log('🔄 Test 2: Calling queueSharePointFolderCreation in sync mode...')
      const jobId = await queueSharePointFolderCreation('schedule', schedule.id, {
        scheduleId: schedule.id,
        customId: schedule.custom_id,
        scheduledDate: schedule.scheduled_date
      }, {
        executeSync: true,
        fallbackToSync: false
      })
      
      console.log(`✅ queueSharePointFolderCreation completed with jobId: ${jobId}`)
      
      // Test 3: Check if SharePoint folder was created
      console.log('🔄 Test 3: Checking SharePoint folder status after creation...')
      const { data: updatedSchedule, error: fetchError } = await supabase
        .from('schedules')
        .select('id, custom_id, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
        .eq('id', schedule.id)
        .single()
      
      if (fetchError) {
        console.error('❌ Error fetching updated schedule:', fetchError)
      } else {
        console.log('📋 Updated SharePoint status:', {
          folder_id: updatedSchedule.sharepoint_folder_id || 'null',
          folder_url: updatedSchedule.sharepoint_folder_url || 'null',
          share_link: updatedSchedule.sharepoint_share_link || 'null'
        })
      }
      
      return NextResponse.json({
        success: true,
        schedule: {
          id: schedule.id,
          custom_id: schedule.custom_id,
          scheduled_date: schedule.scheduled_date
        },
        sharepoint_before: {
          folder_id: schedule.sharepoint_folder_id || null,
          folder_url: schedule.sharepoint_folder_url || null,
          share_link: schedule.sharepoint_share_link || null
        },
        sharepoint_after: {
          folder_id: updatedSchedule?.sharepoint_folder_id || null,
          folder_url: updatedSchedule?.sharepoint_folder_url || null,
          share_link: updatedSchedule?.sharepoint_share_link || null
        },
        job_id: jobId,
        folder_created: !!updatedSchedule?.sharepoint_folder_id,
        message: 'Direct SharePoint test completed'
      })
      
    } catch (importError) {
      console.error('❌ Error importing or calling background-jobs:', importError)
      
      // Test 4: Try importing SharePointService directly
      console.log('🔄 Test 4: Trying SharePointService directly...')
      try {
        const { SharePointService } = await import('@/lib/sharepoint-service')
        console.log('✅ SharePointService import successful')
        
        const result = await SharePointService.ensureScheduleFolder(schedule.id)
        console.log(`✅ SharePointService.ensureScheduleFolder result: ${result}`)
        
        return NextResponse.json({
          success: true,
          schedule: {
            id: schedule.id,
            custom_id: schedule.custom_id,
            scheduled_date: schedule.scheduled_date
          },
          sharepoint_service_result: result,
          method: 'direct_sharepoint_service',
          message: 'Direct SharePointService test completed'
        })
        
      } catch (serviceError) {
        console.error('❌ Error with SharePointService:', serviceError)
        throw serviceError
      }
    }
    
  } catch (error) {
    console.error('❌ Error in SharePoint direct test:', error)
    console.error('❌ Error details:', JSON.stringify(error, null, 2))
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error
      },
      { status: 500 }
    )
  }
}