import { NextRequest, NextResponse } from 'next/server'
import { schedulesApi } from '@/lib/api'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Test endpoint: Creating schedule to test background jobs...')
    
    // Use service role client to bypass RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Find a project and user
    const { data: projects } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1)
    
    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
    
    if (!projects?.length || !users?.length) {
      return NextResponse.json(
        { error: 'No projects or users found' },
        { status: 400 }
      )
    }
    
    const project = projects[0]
    const user = users[0]
    
    console.log(`📋 Using project: ${project.custom_id} ${project.name}`)
    console.log(`👤 Using user: ${user.name}`)
    
    // Create schedule using schedulesApi.create (which should trigger background job)
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      pilot_id: user.id,
      amount: 1500,
      location: 'Test Location for BG Jobs API',
      notes: 'Test schedule created via API to verify background job processing'
    }
    
    console.log('🚀 Creating schedule via direct Supabase insert (bypassing RLS)...')
    
    // Create schedule directly using service role client to bypass RLS
    const { data: schedule, error: createError } = await supabase
      .from('schedules')
      .insert(scheduleData)
      .select(`
        id, custom_id, project_id, scheduled_date, scheduled_end_date, 
        pilot_id, amount, location, notes, created_at
      `)
      .single()
    
    if (createError) {
      throw new Error(`Failed to create schedule: ${createError.message}`)
    }
    
    console.log('✅ Schedule created, now triggering background job manually...')
    
    // Manually trigger the background job logic
    try {
      const { queueSharePointFolderCreation } = await import('@/lib/background-jobs')
      
      const jobId = queueSharePointFolderCreation('schedule', schedule.id, {
        scheduleId: schedule.id,
        customId: schedule.custom_id,
        scheduledDate: schedule.scheduled_date
      })
      
      console.log(`📋 Background job queued: ${jobId}`)
    } catch (jobError) {
      console.error('❌ Failed to queue background job:', jobError)
    }
    
    console.log(`✅ Schedule created: ${schedule.custom_id}`)
    console.log(`🆔 Schedule ID: ${schedule.id}`)
    
    return NextResponse.json({
      success: true,
      schedule: {
        id: schedule.id,
        custom_id: schedule.custom_id,
        scheduled_date: schedule.scheduled_date,
        project_id: schedule.project_id,
        pilot_id: schedule.pilot_id,
        amount: schedule.amount,
        location: schedule.location,
        notes: schedule.notes
      },
      message: 'Schedule created and background job queued'
    })
    
  } catch (error) {
    console.error('❌ Error in test endpoint:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}