import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export async function GET() {
  try {
    // Create a simple Supabase client for testing
    const supabase = createServerClient(supabaseUrl, supabaseAnonKey, {
      cookies: {
        getAll() {
          return []
        },
        setAll() {
          // No-op for testing
        },
      },
    })

    // Get the most recent 3 schedules with SharePoint data
    const { data: schedules, error } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        scheduled_date,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link,
        projects (
          id,
          custom_id,
          name,
          sharepoint_folder_url,
          sharepoint_folder_id,
          clients (
            id,
            custom_id,
            name,
            sharepoint_folder_url,
            sharepoint_folder_id
          )
        )
      `)
      .order('created_at', { ascending: false })
      .limit(3)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Recent schedules retrieved successfully',
      count: schedules?.length || 0,
      schedules: schedules || []
    })

  } catch (error) {
    console.error('Error in test-simple:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}