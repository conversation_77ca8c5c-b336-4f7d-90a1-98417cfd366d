import { NextRequest, NextResponse } from 'next/server'
import { SharePointService } from '@/lib/sharepoint-service'

export async function POST(request: NextRequest) {
  try {
    const { scheduleId } = await request.json()
    
    if (!scheduleId) {
      return NextResponse.json({
        success: false,
        message: 'Schedule ID is required'
      }, { status: 400 })
    }

    console.log(`🧪 Testing SharePoint folder creation for schedule: ${scheduleId}`)
    
    const result = await SharePointService.ensureScheduleFolder(scheduleId)
    
    return NextResponse.json({
      success: true,
      message: 'SharePoint folder creation completed',
      data: result
    })
  } catch (error) {
    console.error('❌ Failed to create SharePoint folder:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to create SharePoint folder',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}