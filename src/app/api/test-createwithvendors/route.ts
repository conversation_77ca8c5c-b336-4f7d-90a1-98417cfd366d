import { NextRequest, NextResponse } from 'next/server'
import { schedulesApi } from '@/lib/api'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Test endpoint: Testing schedulesApi.createWithVendors...')
    
    // Use service role client to bypass RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Find a project and user
    const { data: projects } = await supabase
      .from('projects')
      .select('id, custom_id, name')
      .limit(1)
    
    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)
    
    if (!projects?.length || !users?.length) {
      return NextResponse.json(
        { error: 'No projects or users found' },
        { status: 400 }
      )
    }
    
    const project = projects[0]
    const user = users[0]
    
    console.log(`📋 Using project: ${project.custom_id} ${project.name}`)
    console.log(`👤 Using user: ${user.name}`)
    
    // Create schedule using schedulesApi.createWithVendors
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      pilot_id: user.id,
      amount: 1500,
      location: 'Test Location via schedulesApi.createWithVendors',
      google_maps_link: 'https://maps.google.com',
      notes: 'Test schedule created via schedulesApi.createWithVendors',
      is_recurring: false,
      recurring_pattern: 'weekly' as const,
      is_outsourced: false
    }

    const vendors: any[] = []
    
    console.log('🚀 Creating schedule via service role client (bypassing schedulesApi)...')
    
    // Call the database function directly using service role client
    const { data, error } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: scheduleData.project_id,
      p_scheduled_date: scheduleData.scheduled_date,
      p_scheduled_end_date: scheduleData.scheduled_end_date,
      p_pilot_id: scheduleData.pilot_id,
      p_amount: scheduleData.amount,
      p_location: scheduleData.location,
      p_google_maps_link: scheduleData.google_maps_link,
      p_notes: scheduleData.notes,
      p_is_recurring: scheduleData.is_recurring,
      p_recurring_pattern: scheduleData.recurring_pattern,
      p_is_outsourced: scheduleData.is_outsourced,
      p_vendors: vendors
    })

    if (error) {
      console.error('❌ Database function error:', error)
      throw error
    }

    const schedule = data
    
    console.log(`✅ Schedule created via service role client: ${schedule.custom_id}`)
    console.log(`🆔 Schedule ID: ${schedule.id}`)
    
    return NextResponse.json({
      success: true,
      schedule: {
        id: schedule.id,
        custom_id: schedule.custom_id,
        scheduled_date: schedule.scheduled_date,
        project_id: schedule.project_id,
        pilot_id: schedule.pilot_id,
        amount: schedule.amount,
        location: schedule.location,
        notes: schedule.notes
      },
      message: 'Schedule created via service role client'
    })
    
  } catch (error) {
    console.error('❌ Error in test endpoint:', error)
    console.error('❌ Error details:', JSON.stringify(error, null, 2))
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error
      },
      { status: 500 }
    )
  }
}