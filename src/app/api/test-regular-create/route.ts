import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    if (!supabaseServiceKey) {
      return NextResponse.json({ error: 'Service key not found' }, { status: 500 })
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      supabaseServiceKey
    )

    // Get a project and vendor for testing
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .limit(1)

    if (projectError || !projects || projects.length === 0) {
      return NextResponse.json({ error: 'No projects found', details: projectError?.message }, { status: 500 })
    }

    const project = projects[0]

    // Create a schedule using regular insert (not the RPC function)
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date().toISOString(),
      scheduled_end_date: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours later
      pilot_id: null,
      amount: 1500,
      location: 'Test Location Regular',
      google_maps_link: null,
      notes: 'Test schedule created via regular insert',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }

    const { data: schedule, error: createError } = await supabase
      .from('schedules')
      .insert(scheduleData)
      .select('id, custom_id, project_id, scheduled_date, location, notes')
      .single()

    if (createError) {
      return NextResponse.json({ error: 'Failed to create schedule', details: createError.message }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Schedule created successfully via regular insert',
      schedule
    })

  } catch (error: any) {
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 })
  }
}