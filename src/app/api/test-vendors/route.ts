import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export async function GET() {
  try {
    // Create a simple Supabase client for testing
    const supabase = createServerClient(supabaseUrl, supabaseAnonKey, {
      cookies: {
        getAll() {
          return []
        },
        setAll() {
          // No-op for testing
        },
      },
    })

    // Get vendors
    const { data: vendors, error } = await supabase
      .from('outsourcing_vendors')
      .select('id, custom_id, name')
      .limit(5)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Vendors retrieved successfully',
      count: vendors?.length || 0,
      vendors: vendors || []
    })

  } catch (error) {
    console.error('Error in test-vendors:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}