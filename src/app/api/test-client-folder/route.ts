import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('Testing Microsoft Graph client folder creation...');
    
    // Import the createClientFolder function
    const { createClientFolder } = await import('../../../lib/microsoft-graph');
    
    // Test with dummy data - using a proper UUID format
    const testClientId = '550e8400-e29b-41d4-a716-************';
    const testClientCustomId = 'CYMCL-25-TEST';
    const testClientName = 'Test Client Company';
    
    console.log('Attempting to create folder for:', {
      clientId: testClientId,
      customId: testClientCustomId,
      name: testClientName
    });
    
    const result = await createClientFolder(testClientId, testClientCustomId, testClientName);
    
    console.log('Folder creation successful:', result);
    
    return NextResponse.json({
      success: true,
      message: 'Client folder created successfully',
      result: result
    });
    
  } catch (error) {
    console.error('Error creating client folder:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}