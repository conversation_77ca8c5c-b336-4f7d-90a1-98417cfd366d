'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Circle,
  Play,
  CheckCircle,
  XCircle,
  Minus,
  Edit,
  Trash2,
  Save,
  X,
  Calendar,
  User,
  MessageSquare,
  ExternalLink,
  Target
} from 'lucide-react'
import type { Task } from '@/types'
import { useAuth } from '@/contexts/AuthContext'

interface EnhancedTaskCardProps {
  task: Task
  onEdit?: (task: Task) => void
  onDelete?: (task: Task) => void
  onStatusChange?: (task: Task, newStatus: Task['status']) => void
  onInlineUpdate?: (taskId: string, updates: Partial<Task>) => void
  onCompleteSchedule?: (schedule: any) => void
  schedule?: any
  compact?: boolean
  showRoleFilter?: boolean
  currentUserRole?: string
}

export function EnhancedTaskCard({
  task,
  onEdit,
  onDelete,
  onStatusChange,
  onInlineUpdate,
  onCompleteSchedule,
  schedule,
  compact = false,
  showRoleFilter = true,
  currentUserRole
}: EnhancedTaskCardProps) {
  const { user } = useAuth()
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [editedTitle, setEditedTitle] = useState(task.title)
  const [editedComments, setEditedComments] = useState(task.comments || '')

  // Role-based visibility - Only admin and accounts roles can see tasks
  const shouldShowTask = () => {
    // Always show if role filtering is disabled
    if (!showRoleFilter) return true

    // Only admin and accounts (manager) roles can see and assign tasks
    if (currentUserRole === 'admin' || currentUserRole === 'manager') {
      return true
    }

    // Hide tasks for pilot and editor roles
    return false
  }

  if (!shouldShowTask()) {
    return null
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Circle className="w-4 h-4" />
      case 'in_progress':
        return <Play className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'skipped':
        return <Minus className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      default:
        return <Circle className="w-4 h-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-gray-500 bg-gray-100 border-gray-200'
      case 'in_progress':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'completed':
        return 'text-green-600 bg-green-100 border-green-200'
      case 'skipped':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      case 'cancelled':
        return 'text-red-600 bg-red-100 border-red-200'
      default:
        return 'text-gray-500 bg-gray-100 border-gray-200'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'To Do'
      case 'in_progress':
        return 'In Progress'
      case 'completed':
        return 'Done'
      case 'skipped':
        return 'Skipped'
      case 'cancelled':
        return 'Cancelled'
      default:
        return status
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-100 border-red-200'
      case 'high':
        return 'text-orange-600 bg-orange-100 border-orange-200'
      case 'medium':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'low':
        return 'text-gray-600 bg-gray-100 border-gray-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getRoleColor = (role?: string) => {
    switch (role) {
      case 'pilot':
        return 'text-purple-600 bg-purple-100 border-purple-200'
      case 'editor':
        return 'text-indigo-600 bg-indigo-100 border-indigo-200'
      case 'accounts':
        return 'text-green-600 bg-green-100 border-green-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete the task "${task.title}"?`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(task)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleStatusChange = async (newStatus: Task['status']) => {
    setIsUpdatingStatus(true)
    try {
      await onStatusChange?.(task, newStatus)
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const handleInlineUpdate = async () => {
    if (!onInlineUpdate) return

    try {
      await onInlineUpdate(task.id, {
        title: editedTitle,
        comments: editedComments || null
      })
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update task:', error)
    }
  }

  const cancelEdit = () => {
    setEditedTitle(task.title)
    setEditedComments(task.comments || '')
    setIsEditing(false)
  }

  const handleNavigateToShoot = () => {
    if (task.shoot_id) {
      router.push(`/shoots/${task.shoot_id}`)
    }
  }

  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed'

  if (compact) {
    return (
      <div className={`app-card p-3 ${isOverdue ? 'border-red-300 bg-red-50' : ''}`}>
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                {getStatusIcon(task.status)}
                <span className="ml-1">{getStatusLabel(task.status)}</span>
              </div>
              {task.assigned_role && (
                <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getRoleColor(task.assigned_role)}`}>
                  <User className="w-3 h-3 mr-1" />
                  {task.assigned_role}
                </div>
              )}
              <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                {task.priority}
              </div>
            </div>
            
            {isEditing ? (
              <div className="space-y-2">
                <Input
                  value={editedTitle}
                  onChange={(e) => setEditedTitle(e.target.value)}
                  className="text-sm"
                />
                <Input
                  value={editedComments}
                  onChange={(e) => setEditedComments(e.target.value)}
                  placeholder="Add comments..."
                  className="text-sm"
                />
              </div>
            ) : (
              <div>
                <h4 className="font-medium text-foreground text-sm truncate">{task.title}</h4>
                {task.comments && (
                  <p className="text-xs text-muted-foreground mt-1 flex items-center">
                    <MessageSquare className="w-3 h-3 mr-1" />
                    {task.comments}
                  </p>
                )}
              </div>
            )}
            
            <div className="flex items-center space-x-3 text-xs text-muted-foreground mt-1">
              {task.due_date && (
                <span className={`flex items-center ${isOverdue ? 'text-red-600' : ''}`}>
                  <Calendar className="w-3 h-3 mr-1" />
                  {new Date(task.due_date).toLocaleDateString()}
                </span>
              )}
              {task.assigned_user && (
                <span className="flex items-center">
                  <User className="w-3 h-3 mr-1" />
                  {task.assigned_user.name}
                </span>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-1 ml-2">
            {isEditing ? (
              <>
                <Button variant="outline" size="sm" onClick={handleInlineUpdate}>
                  <Save className="w-3 h-3" />
                </Button>
                <Button variant="outline" size="sm" onClick={cancelEdit}>
                  <X className="w-3 h-3" />
                </Button>
              </>
            ) : (
              <>
                {task.status !== 'completed' && task.status !== 'skipped' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange(task.status === 'pending' ? 'in_progress' : 'completed')}
                    disabled={isUpdatingStatus}
                  >
                    {task.status === 'pending' ? <Play className="w-3 h-3" /> : <CheckCircle className="w-3 h-3" />}
                  </Button>
                )}
                {task.status !== 'completed' && task.status !== 'skipped' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange('skipped')}
                    disabled={isUpdatingStatus}
                  >
                    <Minus className="w-3 h-3" />
                  </Button>
                )}
                {/* Show Complete Schedule button if this task is related to a schedule and schedule is not completed */}
                {schedule && schedule.status !== 'completed' && onCompleteSchedule && task.title.toLowerCase() === 'shoot' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onCompleteSchedule(schedule)}
                    className="text-green-600 hover:text-green-700"
                    title="Complete Schedule"
                  >
                    <Target className="w-3 h-3" />
                  </Button>
                )}
                {task.shoot_id && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNavigateToShoot}
                    title="View Shoot Details"
                  >
                    <ExternalLink className="w-3 h-3" />
                  </Button>
                )}
                {onInlineUpdate && (
                  <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                    <Edit className="w-3 h-3" />
                  </Button>
                )}
                {onEdit && (
                  <Button variant="outline" size="sm" onClick={() => onEdit(task)}>
                    <Edit className="w-3 h-3" />
                  </Button>
                )}
                {onDelete && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Full card view (non-compact)
  return (
    <div className={`app-card hover:shadow-md transition-colors overflow-hidden ${isOverdue ? 'border-red-300' : ''}`}>
      {/* Header */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                {getStatusIcon(task.status)}
                <span className="ml-1">{getStatusLabel(task.status)}</span>
              </div>
              {task.assigned_role && (
                <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getRoleColor(task.assigned_role)}`}>
                  <User className="w-3 h-3 mr-1" />
                  {task.assigned_role}
                </div>
              )}
              <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                {task.priority}
              </div>
            </div>
            
            {isEditing ? (
              <div className="space-y-3">
                <Input
                  value={editedTitle}
                  onChange={(e) => setEditedTitle(e.target.value)}
                  className="font-semibold"
                />
                <textarea
                  value={editedComments}
                  onChange={(e) => setEditedComments(e.target.value)}
                  placeholder="Add comments..."
                  className="w-full p-2 border rounded-md text-sm"
                  rows={3}
                />
              </div>
            ) : (
              <div>
                <h3 className="text-lg font-semibold text-card-foreground mb-1">{task.title}</h3>
                {task.description && (
                  <p className="text-sm text-muted-foreground mb-2">{task.description}</p>
                )}
                {task.comments && (
                  <div className="bg-gray-50 rounded-lg p-3 mb-2">
                    <div className="flex items-center text-sm text-gray-600 mb-1">
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Comments
                    </div>
                    <p className="text-sm text-gray-700">{task.comments}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Task Details */}
        <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground mb-4">
          {task.due_date && (
            <div className={`flex items-center ${isOverdue ? 'text-red-600' : ''}`}>
              <Calendar className="w-4 h-4 mr-2" />
              <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
            </div>
          )}
          {task.assigned_user && (
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2" />
              <span>Assigned to: {task.assigned_user.name}</span>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-200">
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button variant="outline" size="sm" onClick={handleInlineUpdate}>
                  <Save className="w-4 h-4 mr-1" />
                  Save
                </Button>
                <Button variant="outline" size="sm" onClick={cancelEdit}>
                  <X className="w-4 h-4 mr-1" />
                  Cancel
                </Button>
              </>
            ) : (
              <>
                {task.status !== 'completed' && task.status !== 'skipped' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange(task.status === 'pending' ? 'in_progress' : 'completed')}
                    disabled={isUpdatingStatus}
                  >
                    {task.status === 'pending' ? <Play className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                  </Button>
                )}
                {task.status !== 'completed' && task.status !== 'skipped' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange('skipped')}
                    disabled={isUpdatingStatus}
                    title="Mark as Skipped"
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                )}
              </>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Show Complete Schedule button if this task is related to a schedule and schedule is not completed */}
            {!isEditing && schedule && schedule.status !== 'completed' && onCompleteSchedule && task.title.toLowerCase() === 'shoot' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCompleteSchedule(schedule)}
                className="text-green-600 hover:text-green-700"
                title="Complete Schedule"
              >
                <Target className="w-4 h-4 mr-1" />
                Complete Schedule
              </Button>
            )}
            {!isEditing && task.shoot_id && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleNavigateToShoot}
                title="View Shoot Details"
              >
                <ExternalLink className="w-4 h-4 mr-1" />
                Shoot
              </Button>
            )}
            {!isEditing && onInlineUpdate && (
              <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                <Edit className="w-4 h-4" />
              </Button>
            )}
            {!isEditing && onEdit && (
              <Button variant="outline" size="sm" onClick={() => onEdit(task)}>
                <Edit className="w-4 h-4" />
              </Button>
            )}
            {!isEditing && onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
