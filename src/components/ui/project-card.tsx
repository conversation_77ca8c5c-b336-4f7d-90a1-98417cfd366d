'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  MapPin,
  Calendar,
  DollarSign,
  Edit,
  Trash2,
  ExternalLink,
  Building,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  MoreVertical,
  Eye,
  TrendingUp,
  Users
} from 'lucide-react'
import type { Project } from '@/types'
import Link from 'next/link'

interface ProjectCardProps {
  project: Project
  onEdit?: (project: Project) => void
  onDelete?: (project: Project) => void
  onView?: (project: Project) => void
  viewMode?: 'grid' | 'list'
}

export function ProjectCard({ project, onEdit, onDelete, onView, viewMode = 'grid' }: ProjectCardProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-50 dark:bg-green-950/50 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800'
      case 'completed':
        return 'bg-blue-50 dark:bg-blue-950/50 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800'
      case 'on_hold':
        return 'bg-yellow-50 dark:bg-yellow-950/50 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800'
      case 'cancelled':
        return 'bg-red-50 dark:bg-red-950/50 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800'
      default:
        return 'bg-muted text-muted-foreground border-border'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-3 h-3" />
      case 'completed':
        return <CheckCircle className="w-3 h-3" />
      case 'on_hold':
        return <Clock className="w-3 h-3" />
      case 'cancelled':
        return <XCircle className="w-3 h-3" />
      default:
        return <AlertCircle className="w-3 h-3" />
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete "${project.name}"? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(project)
    } finally {
      setIsDeleting(false)
    }
  }

  const progressPercentage = project.total_amount > 0
    ? Math.round((project.amount_received / project.total_amount) * 100)
    : 0

  if (viewMode === 'list') {
    return (
      <div className="group app-card transition-colors p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
              <Link href={`/projects/${project.id}`}>
                <h3 className="text-lg font-semibold text-card-foreground truncate min-w-0 hover:text-primary transition-colors cursor-pointer">
                  {project.name}
                </h3>
              </Link>
              {project.custom_id && (
                <span
                  className="px-2 py-0.5 rounded border border-border text-[10px] sm:text-xs text-muted-foreground bg-muted/50 flex-shrink-0"
                  title="Project ID"
                >
                  {project.custom_id}
                </span>
              )}
              <div className={`flex items-center px-2 py-1 rounded-md text-xs font-medium w-fit flex-shrink-0 ${getStatusColor(project.status)}`}>
                {getStatusIcon(project.status)}
                <span className="ml-1 capitalize">{project.status.replace('_', ' ')}</span>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
              <div className="flex items-center min-w-0">
                <Building className="w-4 h-4 mr-1 flex-shrink-0" />
                <span className="truncate">{project.client?.name}</span>
              </div>
              {project.location && (
                <div className="flex items-center min-w-0">
                  <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
                  <span className="truncate">{project.location}</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between sm:justify-end gap-4 sm:gap-6">
            <div className="text-left sm:text-right min-w-0 flex-shrink-0">
              <div className="text-lg font-bold text-card-foreground truncate">
                {formatCurrency(project.total_amount)}
              </div>
              <div className="text-sm text-muted-foreground">
                {progressPercentage}% received
              </div>
            </div>

            <DropdownMenu
              trigger={
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              }
            >
              {onView && (
                <DropdownMenuItem onClick={() => onView(project)}>
                  <Eye className="w-4 h-4 mr-2" />
                  View Details
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(project)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Project
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Project
                </DropdownMenuItem>
              )}
            </DropdownMenu>
          </div>
        </div>
      </div>
    )
  }

  return (
      <div className="group app-card overflow-hidden will-change-transform">
      {/* Header */}
      <div className="p-4 sm:p-6 pb-3 sm:pb-4">
        <div className="flex items-start justify-between mb-3 sm:mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex flex-col gap-2 mb-3">
              <div className="flex items-start justify-between gap-2">
                <Link href={`/projects/${project.id}`} className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 min-w-0">
                    <h3 className="text-lg font-semibold text-card-foreground truncate hover:text-primary transition-colors cursor-pointer">
                      {project.name}
                    </h3>
                    {project.custom_id && (
                      <span
                        className="px-2 py-0.5 rounded border border-border text-[10px] text-muted-foreground bg-muted/50 flex-shrink-0"
                        title="Project ID"
                      >
                        {project.custom_id}
                      </span>
                    )}
                  </div>
                </Link>
                <DropdownMenu
                  trigger={
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 opacity-60 sm:opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
                    >
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  }
                >
                  {onView && (
                    <DropdownMenuItem onClick={() => onView(project)}>
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                  )}
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(project)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Project
                    </DropdownMenuItem>
                  )}
                  {onDelete && (
                    <DropdownMenuItem
                      onClick={handleDelete}
                      disabled={isDeleting}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Project
                    </DropdownMenuItem>
                  )}
                </DropdownMenu>
              </div>
              <div className={`flex items-center px-2 py-1 rounded-md text-xs font-medium w-fit ${getStatusColor(project.status)}`}>
                {getStatusIcon(project.status)}
                <span className="ml-1 capitalize">{project.status.replace('_', ' ')}</span>
              </div>
            </div>

            <div className="flex items-center text-sm text-muted-foreground mb-3">
              <Building className="w-4 h-4 mr-2 flex-shrink-0" />
              <span className="font-medium truncate">{project.client?.name}</span>
            </div>

            {project.description && (
              <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                {project.description}
              </p>
            )}
          </div>
        </div>

        {/* Location */}
        {project.location && (
          <div className="flex items-center text-sm text-muted-foreground">
            <MapPin className="w-4 h-4 mr-2 flex-shrink-0" />
            <span className="truncate flex-1 min-w-0">{project.location}</span>
            {project.google_maps_link && (
              <a
                href={project.google_maps_link}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-primary hover:text-primary/80 transition-colors flex-shrink-0"
                title="Open in Google Maps"
              >
                <ExternalLink className="w-4 h-4" />
              </a>
            )}
          </div>
        )}
      </div>

      {/* Financial Section */}
      <div className="p-4 sm:p-6 pt-0 space-y-3 sm:space-y-4">
        {/* Amount Cards */}
        <div className="grid grid-cols-2 gap-2 sm:gap-3">
          {/* Total Amount */}
          <div className="bg-muted/50 rounded-lg p-3 sm:p-4 min-w-0">
            <div className="flex items-center justify-between mb-1 sm:mb-2">
              <DollarSign className="w-4 h-4 text-primary flex-shrink-0" />
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Total</span>
            </div>
            <div className="text-lg sm:text-xl font-bold text-card-foreground truncate">
              {formatCurrency(project.total_amount)}
            </div>
            {project.gst_inclusive && (
              <div className="text-xs text-muted-foreground mt-1">inc. GST</div>
            )}
          </div>

          {/* Received Amount */}
          <div className="bg-green-50 dark:bg-green-950/30 rounded-lg p-3 sm:p-4 min-w-0">
            <div className="flex items-center justify-between mb-1 sm:mb-2">
              <TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400 flex-shrink-0" />
              <span className="text-xs font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">Received</span>
            </div>
            <div className="text-lg sm:text-xl font-bold text-green-700 dark:text-green-300 truncate">
              {formatCurrency(project.amount_received)}
            </div>
            <div className="text-xs text-green-600/70 dark:text-green-400/70 mt-1">
              {progressPercentage}% of total
            </div>
          </div>
        </div>

        {/* Pending Amount (if any) */}
        {project.amount_pending > 0 && (
          <div className="bg-orange-50 dark:bg-orange-950/30 rounded-lg p-3 sm:p-4">
            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center min-w-0 flex-1">
                <Clock className="w-4 h-4 text-orange-600 dark:text-orange-400 mr-2 flex-shrink-0" />
                <div className="min-w-0">
                  <div className="text-sm font-medium text-orange-700 dark:text-orange-300">Pending Payment</div>
                  <div className="text-xs text-orange-600/70 dark:text-orange-400/70">Outstanding amount</div>
                </div>
              </div>
              <div className="text-lg font-bold text-orange-700 dark:text-orange-300 flex-shrink-0">
                {formatCurrency(project.amount_pending)}
              </div>
            </div>
          </div>
        )}

        {/* Progress Bar */}
        <div>
          <div className="flex items-center justify-between text-sm font-medium text-card-foreground mb-2">
            <span>Payment Progress</span>
            <span className="text-primary">{progressPercentage}%</span>
          </div>
          <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
            <div
              className={`h-2 rounded-full transition-all duration-500 ease-out ${
                progressPercentage === 100
                  ? 'bg-green-500'
                  : progressPercentage > 50
                  ? 'bg-primary'
                  : 'bg-orange-500'
              }`}
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="px-4 sm:px-6 py-3 sm:py-4 border-t border-border">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // TODO: Implement open files functionality
              console.log('Open files for project:', project.id)
            }}
            className="flex-1 text-xs font-medium"
          >
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
            </svg>
            <span className="hidden sm:inline">Open Files</span>
            <span className="sm:hidden">Files</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // TODO: Implement share link functionality
              console.log('Share link for project:', project.id)
            }}
            className="flex-1 text-xs font-medium"
          >
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            <span className="hidden sm:inline">Share Link</span>
            <span className="sm:hidden">Share</span>
          </Button>
        </div>
      </div>
    </div>
  )
}
