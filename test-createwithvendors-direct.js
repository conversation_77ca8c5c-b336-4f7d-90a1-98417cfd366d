#!/usr/bin/env node

/**
 * Test script to directly test the createWithVendors method
 * This simulates exactly what the UI does when creating a schedule
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testCreateWithVendorsDirect() {
  console.log('🧪 Testing createWithVendors Method Directly')
  console.log('=' .repeat(50))

  try {
    // Step 1: Get test data
    console.log('\n1️⃣ Getting test data...')
    
    const { data: projects } = await supabase
      .from('projects')
      .select(`
        id, 
        custom_id, 
        name,
        client_id,
        clients!inner (
          id,
          custom_id,
          name
        )
      `)
      .limit(1)

    const { data: users } = await supabase
      .from('users')
      .select('id, name')
      .limit(1)

    if (!projects?.length || !users?.length) {
      console.log('❌ No projects or users found')
      return
    }

    const project = projects[0]
    const user = users[0]
    
    console.log(`   📋 Using project: ${project.custom_id} ${project.name}`)
    console.log(`   🏢 Client: ${project.clients.custom_id} ${project.clients.name}`)

    // Step 2: Call the database function directly (this is what createWithVendors does)
    console.log('\n2️⃣ Calling create_schedule_with_vendors function...')
    
    const scheduleData = {
      project_id: project.id,
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      scheduled_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
      pilot_id: user.id,
      amount: 2000,
      location: 'Test Location via createWithVendors Direct',
      google_maps_link: 'https://maps.google.com',
      notes: 'Test schedule created via direct createWithVendors call',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: false
    }

    const vendors = []

    // This is the exact call that schedulesApi.createWithVendors makes
    const { data: schedule, error: createError } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: scheduleData.project_id,
      p_scheduled_date: scheduleData.scheduled_date,
      p_scheduled_end_date: scheduleData.scheduled_end_date,
      p_pilot_id: scheduleData.pilot_id,
      p_amount: scheduleData.amount,
      p_location: scheduleData.location,
      p_google_maps_link: scheduleData.google_maps_link,
      p_notes: scheduleData.notes,
      p_is_recurring: scheduleData.is_recurring,
      p_recurring_pattern: scheduleData.recurring_pattern,
      p_is_outsourced: !!scheduleData.is_outsourced,
      p_vendors: vendors
    })

    if (createError) {
      console.log('❌ Error creating schedule:', createError)
      return
    }

    console.log(`   ✅ Schedule created: ${schedule.custom_id} (ID: ${schedule.id})`)

    // Step 3: Now simulate the SharePoint folder creation logic from createWithVendors
    console.log('\n3️⃣ Simulating SharePoint folder creation logic...')
    
    try {
      console.log('Processing SharePoint folder creation for schedule:', schedule.id)
      
      // Determine execution mode from environment variable (same as in createWithVendors)
      const mode = process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'background-with-fallback'
      
      let executeSync = false
      let fallbackToSync = false
      
      switch (mode) {
        case 'sync':
          executeSync = true
          fallbackToSync = false
          break
        case 'background':
          executeSync = false
          fallbackToSync = false
          break
        case 'background-with-fallback':
        default:
          executeSync = false
          fallbackToSync = true
          break
      }
      
      console.log(`   SharePoint folder creation mode: ${mode} (executeSync: ${executeSync}, fallbackToSync: ${fallbackToSync})`)
      
      // Test the background job execution
      const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folder-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entityType: 'schedule',
          entityId: schedule.id,
          payload: {
            scheduleId: schedule.id,
            customId: schedule.custom_id,
            scheduledDate: scheduleData.scheduled_date
          }
        })
      })

      const result = await response.json()
      
      if (result.success) {
        console.log('   ✅ SharePoint folder creation job executed successfully')
        console.log(`   📋 Job ID: ${result.jobId}`)
      } else {
        console.log('   ❌ SharePoint folder creation job failed:', result.message)
      }
      
    } catch (jobError) {
      console.log('   ❌ Error processing SharePoint folder creation:', jobError.message)
    }

    // Step 4: Check if folder was created
    console.log('\n4️⃣ Checking if SharePoint folder was created...')
    
    const { data: updatedSchedule } = await supabase
      .from('schedules')
      .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
      .eq('id', schedule.id)
      .single()

    if (updatedSchedule?.sharepoint_folder_id) {
      console.log('   ✅ SharePoint folder was created!')
      console.log(`   📁 Folder ID: ${updatedSchedule.sharepoint_folder_id}`)
      console.log(`   🔗 Folder URL: ${updatedSchedule.sharepoint_folder_url}`)
      console.log(`   📤 Share Link: ${updatedSchedule.sharepoint_share_link}`)
    } else {
      console.log('   ❌ SharePoint folder was NOT created')
      
      // Try manual trigger
      console.log('\n5️⃣ Attempting manual SharePoint folder creation...')
      
      try {
        const manualResponse = await fetch('http://localhost:3000/api/test-sharepoint', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            scheduleId: schedule.id
          })
        })

        const manualResult = await manualResponse.json()
        
        if (manualResult.success) {
          console.log('   ✅ Manual SharePoint folder creation succeeded')
          
          // Check database again
          const { data: manualCheck } = await supabase
            .from('schedules')
            .select('sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link')
            .eq('id', schedule.id)
            .single()

          if (manualCheck?.sharepoint_folder_id) {
            console.log(`   📁 Folder ID: ${manualCheck.sharepoint_folder_id}`)
            console.log(`   🔗 Folder URL: ${manualCheck.sharepoint_folder_url}`)
            console.log(`   📤 Share Link: ${manualCheck.sharepoint_share_link}`)
          }
        } else {
          console.log('   ❌ Manual SharePoint folder creation failed:', manualResult.message)
        }
      } catch (manualError) {
        console.log('   ❌ Error during manual trigger:', manualError.message)
      }
    }

    // Step 6: Environment diagnostics
    console.log('\n6️⃣ Environment Diagnostics...')
    console.log(`   🔧 SHAREPOINT_FOLDER_CREATION_MODE: ${process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'not set'}`)
    console.log(`   🌐 NODE_ENV: ${process.env.NODE_ENV || 'not set'}`)

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testCreateWithVendorsDirect()
