#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkRecentSchedule() {
  console.log('🔍 Checking recent schedule and its tasks...');
  
  // Get the most recent schedule
  const { data: schedules, error } = await supabase
    .from('schedules')
    .select(`
      id,
      custom_id,
      project_id,
      scheduled_date,
      created_at,
      project:projects!inner (
        id,
        name,
        client:clients!inner (
          client_type
        )
      )
    `)
    .order('created_at', { ascending: false })
    .limit(1);
    
  if (error) {
    console.error('Error fetching schedules:', error);
    return;
  }
  
  if (!schedules || schedules.length === 0) {
    console.log('No schedules found');
    return;
  }
  
  const schedule = schedules[0];
  console.log('📅 Recent schedule:', {
    id: schedule.id,
    custom_id: schedule.custom_id,
    project_id: schedule.project_id,
    client_type: schedule.project?.client?.client_type,
    created_at: schedule.created_at
  });
  
  // Check tasks for this schedule
  const { data: tasks, error: tasksError } = await supabase
    .from('tasks')
    .select('id, title, shoot_id, project_id, created_at')
    .eq('project_id', schedule.project_id)
    .order('created_at', { ascending: false });
    
  if (tasksError) {
    console.error('Error fetching tasks:', tasksError);
    return;
  }
  
  console.log(`📋 Tasks for project ${schedule.project_id}:`, tasks?.length || 0);
  
  if (tasks && tasks.length > 0) {
    tasks.forEach(task => {
      console.log(`  - ${task.title} (shoot_id: ${task.shoot_id || 'null'}, created: ${task.created_at})`);
    });
  } else {
    console.log('❌ No tasks found for this project!');
    console.log('Client type:', schedule.project?.client?.client_type);
  }
  
  // Check if there are any users with roles for task assignment
  console.log('\n👥 Checking users by role...');
  const { data: users, error: usersError } = await supabase
    .from('users')
    .select('id, name, role')
    .order('role');
    
  if (usersError) {
    console.error('Error fetching users:', usersError);
    return;
  }
  
  const usersByRole = {
    pilot: users?.filter(u => u.role === 'pilot') || [],
    editor: users?.filter(u => u.role === 'editor') || [],
    accounts: users?.filter(u => u.role === 'accounts' || u.role === 'admin') || []
  };
  
  console.log('Users by role:', {
    pilot: usersByRole.pilot.length,
    editor: usersByRole.editor.length,
    accounts: usersByRole.accounts.length
  });
  
  if (usersByRole.pilot.length === 0 && usersByRole.editor.length === 0 && usersByRole.accounts.length === 0) {
    console.log('⚠️ No users with proper roles found - this could prevent task creation!');
  }
}

checkRecentSchedule().catch(console.error);