async function debugVendorPaymentSync() {
  console.log('🔍 DEBUGGING VENDOR PAYMENT SYNC ISSUE')
  console.log('=====================================')
  
  // Test with a known vendor ID
  const vendorId = '966f67ef-bbc8-43f6-9d95-5d7978d461b1' // Arun photography
  
  console.log(`\n1️⃣ TESTING VENDOR-SHOOTS API:`)
  try {
    const response = await fetch(`http://localhost:3000/api/vendor-shoots?vendorId=${vendorId}`)
    const apiData = await response.json()
    
    if (!response.ok) {
      console.error('❌ API error:', apiData)
      return
    }
    
    console.log(`✅ API returned ${apiData.shoots?.length || 0} shoots`)
    
    // Check if the API data includes expense information
    if (apiData.shoots && apiData.shoots.length > 0) {
      const firstShoot = apiData.shoots[0]
      console.log('\n📋 Sample shoot data structure:')
      console.log('- Has project:', !!firstShoot.project)
      console.log('- Has project.payments:', !!firstShoot.project?.payments)
      console.log('- Has vendors:', !!firstShoot.vendors)
      
      if (firstShoot.project) {
        console.log('- Project ID:', firstShoot.project.id)
        console.log('- Project vendor_payment_status:', firstShoot.project.vendor_payment_status)
        console.log('- Project vendor_payment_amount:', firstShoot.project.vendor_payment_amount)
      }
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message)
  }
  
  console.log(`\n2️⃣ CHECKING OUTSOURCING EXPENSES:`)
  
  // Get all projects that have schedules with this vendor
  const { data: scheduleVendors, error: svError } = await supabase
    .from('schedule_vendors')
    .select(`
      schedule_id,
      schedules!inner (
        project_id,
        project:projects(name)
      )
    `)
    .eq('vendor_id', vendorId)
  
  if (svError) {
    console.error('❌ Error fetching schedule vendors:', svError)
    return
  }
  
  const projectIds = [...new Set(scheduleVendors.map(sv => sv.schedules.project_id))]
  console.log(`Found ${projectIds.length} projects with this vendor`)
  
  // Check outsourcing expenses for these projects
  for (const projectId of projectIds) {
    const { data: expenses, error: expError } = await supabase
      .from('expenses')
      .select('*')
      .eq('project_id', projectId)
      .eq('category', 'outsourcing')
      .eq('vendor_id', vendorId)
    
    if (expError) {
      console.error(`❌ Error fetching expenses for project ${projectId}:`, expError)
      continue
    }
    
    const project = scheduleVendors.find(sv => sv.schedules.project_id === projectId)?.schedules.project
    console.log(`\n📊 Project: ${project?.name || projectId}`)
    console.log(`   Outsourcing expenses: ${expenses?.length || 0}`)
    
    if (expenses && expenses.length > 0) {
      const totalPaid = expenses.reduce((sum, exp) => sum + exp.amount, 0)
      console.log(`   Total paid: ₹${totalPaid}`)
      expenses.forEach((exp, index) => {
        console.log(`   ${index + 1}. ₹${exp.amount} - ${exp.description} (${exp.date})`)
      })
    }
  }
  
  console.log('\n🎯 CONCLUSION:')
  console.log('The vendor-shoots API likely needs to include outsourcing expense data')
  console.log('to properly calculate vendor payment status on the vendor details page.')
}

debugVendorPaymentSync().catch(console.error)