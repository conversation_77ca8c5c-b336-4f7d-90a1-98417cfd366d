// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

async function checkMissingSharePointFolders() {
  try {
    console.log('🔍 Checking for schedules missing SharePoint folders...')
    
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    // Get recent schedules and check which ones are missing SharePoint folder info
    const { data: schedules, error } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        scheduled_date,
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link,
        projects!inner (
          id,
          custom_id,
          name,
          clients!inner (
            id,
            custom_id,
            name
          )
        )
      `)
      .order('created_at', { ascending: false })
      .limit(10)
    
    if (error) {
      console.error('❌ Error fetching schedules:', error)
      return
    }
    
    console.log(`📊 Found ${schedules.length} recent schedules`)
    
    let missingCount = 0
    let hasCount = 0
    
    for (const schedule of schedules) {
      const project = schedule.projects
      const client = project.clients
      
      const hasFolderInfo = !!(schedule.sharepoint_folder_id && schedule.sharepoint_folder_url)
      
      if (hasFolderInfo) {
        hasCount++
        console.log(`✅ ${schedule.custom_id} - Has SharePoint folder`)
      } else {
        missingCount++
        console.log(`❌ ${schedule.custom_id} - Missing SharePoint folder`)
        console.log(`   📅 Date: ${schedule.scheduled_date}`)
        console.log(`   🏢 Client: ${client.custom_id} ${client.name}`)
        console.log(`   📁 Project: ${project.custom_id} ${project.name}`)
        console.log(`   🆔 Schedule ID: ${schedule.id}`)
        console.log('')
      }
    }
    
    console.log(`\n📈 Summary:`)
    console.log(`   ✅ Schedules with folders: ${hasCount}`)
    console.log(`   ❌ Schedules missing folders: ${missingCount}`)
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

checkMissingSharePointFolders()