// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

async function testScheduleCreationFlow() {
  try {
    console.log('🧪 Testing Schedule Creation and SharePoint Folder Flow...\n')
    
    // Step 1: Check environment variables
    console.log('1️⃣ Checking Environment Variables:')
    const requiredEnvVars = [
      'MICROSOFT_CLIENT_ID',
      'MICROSOFT_CLIENT_SECRET', 
      'MICROSOFT_TENANT_ID',
      'NEXT_PUBLIC_SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY'
    ]
    
    let missingVars = []
    for (const varName of requiredEnvVars) {
      if (process.env[varName]) {
        console.log(`   ✅ ${varName}: Set`)
      } else {
        console.log(`   ❌ ${varName}: Missing`)
        missingVars.push(varName)
      }
    }
    
    if (missingVars.length > 0) {
      console.log(`\n❌ Missing environment variables: ${missingVars.join(', ')}`)
      return
    }
    
    // Step 2: Test Microsoft Graph authentication
    console.log('\n2️⃣ Testing Microsoft Graph Authentication:')
    try {
      const response = await fetch(
        `https://login.microsoftonline.com/${process.env.MICROSOFT_TENANT_ID}/oauth2/v2.0/token`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: new URLSearchParams({
            client_id: process.env.MICROSOFT_CLIENT_ID,
            scope: 'https://graph.microsoft.com/.default',
            client_secret: process.env.MICROSOFT_CLIENT_SECRET,
            grant_type: 'client_credentials'
          })
        }
      )
      
      if (response.ok) {
        console.log('   ✅ Microsoft Graph authentication successful')
      } else {
        console.log('   ❌ Microsoft Graph authentication failed:', response.status)
        const errorText = await response.text()
        console.log('   Error details:', errorText)
        return
      }
    } catch (error) {
      console.log('   ❌ Microsoft Graph authentication error:', error.message)
      return
    }
    
    // Step 3: Check recent schedules
    console.log('\n3️⃣ Checking Recent Schedules:')
    const { createClient } = require('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )
    
    const { data: recentSchedules, error } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        created_at,
        sharepoint_folder_id,
        sharepoint_folder_url,
        projects!inner (
          custom_id,
          name,
          clients!inner (
            custom_id,
            name
          )
        )
      `)
      .order('created_at', { ascending: false })
      .limit(3)
    
    if (error) {
      console.log('   ❌ Error fetching schedules:', error.message)
      return
    }
    
    console.log(`   📊 Found ${recentSchedules.length} recent schedules:`)
    for (const schedule of recentSchedules) {
      const hasFolder = !!(schedule.sharepoint_folder_id && schedule.sharepoint_folder_url)
      const project = schedule.projects
      const client = project.clients
      
      console.log(`   ${hasFolder ? '✅' : '❌'} ${schedule.custom_id} (${client.custom_id} ${client.name} / ${project.custom_id} ${project.name})`)
      console.log(`      Created: ${new Date(schedule.created_at).toLocaleString()}`)
      if (hasFolder) {
        console.log(`      SharePoint: ${schedule.sharepoint_folder_url}`)
      }
    }
    
    // Step 4: Test SharePoint folder creation for the most recent schedule without folder
    const scheduleWithoutFolder = recentSchedules.find(s => !s.sharepoint_folder_id)
    
    if (scheduleWithoutFolder) {
      console.log(`\n4️⃣ Testing SharePoint Folder Creation for ${scheduleWithoutFolder.custom_id}:`)
      
      try {
        // Call the ensure-sharepoint-folders API
        const response = await fetch('http://localhost:3000/api/ensure-sharepoint-folders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          const result = await response.json()
          console.log('   ✅ API call successful')
          console.log('   Result:', result)
        } else {
          console.log('   ❌ API call failed:', response.status)
          const errorText = await response.text()
          console.log('   Error:', errorText)
        }
      } catch (error) {
        console.log('   ❌ API call error:', error.message)
      }
    } else {
      console.log('\n4️⃣ All recent schedules already have SharePoint folders ✅')
    }
    
    // Step 5: Check if development server is running
    console.log('\n5️⃣ Checking Development Server:')
    try {
      const response = await fetch('http://localhost:3000/api/check-schedule', {
        method: 'GET'
      })
      
      if (response.ok) {
        console.log('   ✅ Development server is running on port 3000')
      } else {
        console.log('   ⚠️  Development server responded with status:', response.status)
      }
    } catch (error) {
      console.log('   ❌ Development server not accessible:', error.message)
      console.log('   💡 Make sure to run: npm run dev')
    }
    
    console.log('\n🎯 Test Complete!')
    
  } catch (error) {
    console.error('❌ Test error:', error)
  }
}

testScheduleCreationFlow()